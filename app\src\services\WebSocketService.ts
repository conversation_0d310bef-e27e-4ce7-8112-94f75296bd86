/**
 * WebSocket Service for Real-time Messaging
 * Replaces polling with instant message delivery
 * No typing indicators or online status as per requirements
 */

import { AuthService } from './AuthService';
import { ChatMessage } from './ChatService';
import AppConfig from '../config/app.config';

interface WebSocketMessage {
  type: 'message' | 'media' | 'system' | 'error' | 'status_update';
  data: any;
  timestamp: number;
}

type MessageHandler = (message: ChatMessage) => void;
type ConnectionHandler = (connected: boolean) => void;
type ErrorHandler = (error: string) => void;

export class WebSocketService {
  private ws: WebSocket | null = null;
  private authService: AuthService;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private isConnecting = false;
  private shouldReconnect = true;

  // Event handlers
  private messageHandlers: MessageHandler[] = [];
  private connectionHandlers: ConnectionHandler[] = [];
  private errorHandlers: ErrorHandler[] = [];

  constructor(authService: AuthService) {
    this.authService = authService;
  }

  /**
   * Connect to WebSocket server
   */
  public async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    this.shouldReconnect = true;

    try {
      const token = this.authService.getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Convert HTTP URL to WebSocket URL
      const wsUrl = AppConfig.backend.baseUrl
        .replace('http://', 'ws://')
        .replace('https://', 'wss://');

      const url = `${wsUrl}/ws/chat?token=${encodeURIComponent(token)}`;
      
      console.log('🔌 Connecting to WebSocket:', url.replace(token, 'TOKEN_HIDDEN'));

      this.ws = new WebSocket(url);
      this.setupEventHandlers();

    } catch (error) {
      console.error('❌ WebSocket connection failed:', error);
      this.isConnecting = false;
      this.handleError('Failed to connect to real-time messaging');
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  public disconnect(): void {
    this.shouldReconnect = false;
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnecting = false;
    this.reconnectAttempts = 0;
    console.log('🔌 WebSocket disconnected');
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('✅ WebSocket connected successfully');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      this.notifyConnectionHandlers(true);
    };

    this.ws.onmessage = (event) => {
      try {
        const wsMessage: WebSocketMessage = JSON.parse(event.data);
        this.handleWebSocketMessage(wsMessage);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('🔌 WebSocket connection closed:', event.code, event.reason);
      this.isConnecting = false;
      this.notifyConnectionHandlers(false);
      
      if (this.shouldReconnect && event.code !== 1000) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
      this.isConnecting = false;
      this.handleError('WebSocket connection error');
    };
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleWebSocketMessage(wsMessage: WebSocketMessage): void {
    console.log('📨 WebSocket message received:', wsMessage.type);

    switch (wsMessage.type) {
      case 'message':
      case 'media':
        this.handleIncomingMessage(wsMessage.data);
        break;

      case 'status_update':
        console.log('📊 Message status update:', wsMessage.data);
        // Handle message status updates (delivered, read, etc.)
        break;

      case 'system':
        console.log('ℹ️ System message:', wsMessage.data.message);
        break;

      case 'error':
        console.error('❌ Server error:', wsMessage.data.message);
        this.handleError(wsMessage.data.message);
        break;

      default:
        console.warn('⚠️ Unknown message type:', wsMessage.type);
    }
  }

  /**
   * Handle incoming chat message
   */
  private handleIncomingMessage(messageData: any): void {
    try {
      // Validate message data
      if (!messageData || !messageData.id) {
        console.warn('⚠️ Invalid message data received:', messageData);
        return;
      }

      // Safely parse timestamp
      let timestamp = Date.now();
      if (messageData.timestamp) {
        const parsed = new Date(messageData.timestamp);
        if (!isNaN(parsed.getTime())) {
          timestamp = parsed.getTime();
        }
      }

      // Convert WebSocket message to ChatMessage format
      const chatMessage: ChatMessage = {
        id: messageData.id,
        chatId: messageData.chatId || 'default-chat',
        text: messageData.content || '',
        sender: messageData.isOwnMessage ? 'user' : 'superuser',
        timestamp,
        status: 'delivered',
        isDelivered: true,
        isRead: false,
        attachment: messageData.attachment ? {
          id: messageData.mediaId || `attachment_${messageData.id}`,
          name: messageData.attachment.name || 'Unknown file',
          type: this.getFileCategory(messageData.attachment.type || 'application/octet-stream'),
          size: messageData.attachment.size || 0,
          uri: messageData.mediaId ? `${AppConfig.backend.baseUrl}/api/chat/unified/media/${messageData.mediaId}` : '',
          mimeType: messageData.attachment.type || 'application/octet-stream'
        } : undefined
      };

      console.log('📨 Processed WebSocket message:', {
        id: chatMessage.id,
        sender: chatMessage.sender,
        hasAttachment: !!chatMessage.attachment,
        timestamp: chatMessage.timestamp
      });

      // Notify all message handlers
      this.messageHandlers.forEach(handler => {
        try {
          handler(chatMessage);
        } catch (error) {
          console.error('❌ Error in message handler:', error);
        }
      });

    } catch (error) {
      console.error('❌ Error processing incoming message:', error, messageData);
    }
  }

  /**
   * Get file category from MIME type
   */
  private getFileCategory(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf') || mimeType.includes('document')) return 'document';
    return 'file';
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (!this.shouldReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Max reconnection attempts reached');
      this.handleError('Unable to maintain real-time connection');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    console.log(`🔄 Scheduling WebSocket reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect();
      }
    }, delay);
  }

  /**
   * Send ping to keep connection alive
   */
  public ping(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type: 'ping' }));
    }
  }

  /**
   * Check if WebSocket is connected
   */
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Add message handler
   */
  public onMessage(handler: MessageHandler): void {
    this.messageHandlers.push(handler);
  }

  /**
   * Remove message handler
   */
  public removeMessageHandler(handler: MessageHandler): void {
    const index = this.messageHandlers.indexOf(handler);
    if (index > -1) {
      this.messageHandlers.splice(index, 1);
    }
  }

  /**
   * Add connection handler
   */
  public onConnection(handler: ConnectionHandler): void {
    this.connectionHandlers.push(handler);
  }

  /**
   * Add error handler
   */
  public onError(handler: ErrorHandler): void {
    this.errorHandlers.push(handler);
  }

  /**
   * Notify connection handlers
   */
  private notifyConnectionHandlers(connected: boolean): void {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        console.error('❌ Error in connection handler:', error);
      }
    });
  }

  /**
   * Handle errors
   */
  private handleError(message: string): void {
    this.errorHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('❌ Error in error handler:', error);
      }
    });
  }
}

export default WebSocketService;
