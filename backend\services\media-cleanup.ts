/**
 * Media Cleanup Service
 * Implements automated cleanup for orphaned media files and proper lifecycle management
 * Fixes storage bloat and security risks from abandoned files
 */

import fs from 'fs/promises';
import path from 'path';
import { existsSync } from 'fs';
import Media from '../models/Media';
import { Message } from '../models/Chat';
import AuditLogModel from '../models/AuditLog';
import cron from 'node-cron';

interface CleanupStats {
  orphanedMediaRecords: number;
  orphanedFiles: number;
  expiredMedia: number;
  totalSpaceFreed: number;
  errors: string[];
}

interface CleanupOptions {
  dryRun?: boolean;
  maxAge?: number; // Days
  includeActive?: boolean;
  batchSize?: number;
}

export class MediaCleanupService {
  private uploadDir: string;
  private thumbnailDir: string;
  private isRunning = false;

  constructor() {
    this.uploadDir = path.join(process.cwd(), 'uploads', 'media');
    this.thumbnailDir = path.join(process.cwd(), 'uploads', 'thumbnails');
  }

  /**
   * Initialize automated cleanup scheduling
   */
  public initialize(): void {
    // Run cleanup daily at 2 AM
    cron.schedule('0 2 * * *', async () => {
      console.log('🧹 Starting scheduled media cleanup...');
      await this.performCleanup({
        maxAge: 30, // Clean files older than 30 days
        batchSize: 100
      });
    });

    // Run orphaned file cleanup every 6 hours
    cron.schedule('0 */6 * * *', async () => {
      console.log('🧹 Starting orphaned file cleanup...');
      await this.cleanupOrphanedFiles();
    });

    console.log('✅ Media cleanup service initialized with automated scheduling');
  }

  /**
   * Perform comprehensive media cleanup
   */
  public async performCleanup(options: CleanupOptions = {}): Promise<CleanupStats> {
    if (this.isRunning) {
      throw new Error('Cleanup is already running');
    }

    this.isRunning = true;
    const stats: CleanupStats = {
      orphanedMediaRecords: 0,
      orphanedFiles: 0,
      expiredMedia: 0,
      totalSpaceFreed: 0,
      errors: []
    };

    try {
      console.log('🧹 Starting media cleanup with options:', options);

      // Step 1: Clean orphaned media records
      const orphanedRecords = await this.cleanupOrphanedMediaRecords(options);
      stats.orphanedMediaRecords = orphanedRecords.count;
      stats.totalSpaceFreed += orphanedRecords.spaceFreed;

      // Step 2: Clean orphaned files
      const orphanedFiles = await this.cleanupOrphanedFiles(options);
      stats.orphanedFiles = orphanedFiles.count;
      stats.totalSpaceFreed += orphanedFiles.spaceFreed;

      // Step 3: Clean expired media
      const expiredMedia = await this.cleanupExpiredMedia(options);
      stats.expiredMedia = expiredMedia.count;
      stats.totalSpaceFreed += expiredMedia.spaceFreed;

      // Log cleanup results
      await this.logCleanupResults(stats, options);

      console.log('✅ Media cleanup completed:', stats);

    } catch (error) {
      console.error('❌ Media cleanup failed:', error);
      stats.errors.push(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      this.isRunning = false;
    }

    return stats;
  }

  /**
   * Clean up media records that have no associated message
   */
  private async cleanupOrphanedMediaRecords(options: CleanupOptions): Promise<{ count: number; spaceFreed: number }> {
    let count = 0;
    let spaceFreed = 0;

    try {
      // Find media records without associated messages
      const orphanedMedia = await Media.find({
        messageId: { $exists: true },
        isActive: true
      });

      for (const media of orphanedMedia) {
        if (media.messageId) {
          const messageExists = await Message.findById(media.messageId);
          if (!messageExists) {
            // This media record is orphaned
            const fileSize = await this.getFileSize(media.storage.encryptedPath);
            const thumbnailSize = media.storage.thumbnailPath 
              ? await this.getFileSize(media.storage.thumbnailPath) 
              : 0;

            if (!options.dryRun) {
              // Delete files
              await this.deleteFile(media.storage.encryptedPath);
              if (media.storage.thumbnailPath) {
                await this.deleteFile(media.storage.thumbnailPath);
              }

              // Mark media record as inactive
              media.isActive = false;
              media.metadata.deletedAt = new Date();
              media.metadata.deletionReason = 'orphaned_cleanup';
              await media.save();
            }

            count++;
            spaceFreed += fileSize + thumbnailSize;
            console.log(`🗑️ Cleaned orphaned media: ${media.mediaId} (${fileSize + thumbnailSize} bytes)`);
          }
        }
      }

    } catch (error) {
      console.error('❌ Error cleaning orphaned media records:', error);
    }

    return { count, spaceFreed };
  }

  /**
   * Clean up files that exist on disk but have no database record
   */
  private async cleanupOrphanedFiles(options: CleanupOptions = {}): Promise<{ count: number; spaceFreed: number }> {
    let count = 0;
    let spaceFreed = 0;

    try {
      // Check upload directory
      if (existsSync(this.uploadDir)) {
        const uploadFiles = await fs.readdir(this.uploadDir);
        for (const fileName of uploadFiles) {
          const filePath = path.join(this.uploadDir, fileName);
          const mediaRecord = await Media.findOne({
            'storage.encryptedPath': filePath,
            isActive: true
          });

          if (!mediaRecord) {
            const fileSize = await this.getFileSize(filePath);
            
            if (!options.dryRun) {
              await this.deleteFile(filePath);
            }
            
            count++;
            spaceFreed += fileSize;
            console.log(`🗑️ Cleaned orphaned file: ${fileName} (${fileSize} bytes)`);
          }
        }
      }

      // Check thumbnail directory
      if (existsSync(this.thumbnailDir)) {
        const thumbnailFiles = await fs.readdir(this.thumbnailDir);
        for (const fileName of thumbnailFiles) {
          const filePath = path.join(this.thumbnailDir, fileName);
          const mediaRecord = await Media.findOne({
            'storage.thumbnailPath': filePath,
            isActive: true
          });

          if (!mediaRecord) {
            const fileSize = await this.getFileSize(filePath);
            
            if (!options.dryRun) {
              await this.deleteFile(filePath);
            }
            
            count++;
            spaceFreed += fileSize;
            console.log(`🗑️ Cleaned orphaned thumbnail: ${fileName} (${fileSize} bytes)`);
          }
        }
      }

    } catch (error) {
      console.error('❌ Error cleaning orphaned files:', error);
    }

    return { count, spaceFreed };
  }

  /**
   * Clean up expired media based on access rules
   */
  private async cleanupExpiredMedia(options: CleanupOptions): Promise<{ count: number; spaceFreed: number }> {
    let count = 0;
    let spaceFreed = 0;

    try {
      const maxAge = options.maxAge || 90; // Default 90 days
      const cutoffDate = new Date(Date.now() - maxAge * 24 * 60 * 60 * 1000);

      // Find expired media
      const expiredMedia = await Media.find({
        $or: [
          { 'access.expiresAt': { $lt: new Date() } },
          { 
            createdAt: { $lt: cutoffDate },
            'metadata.lastAccessedAt': { $lt: cutoffDate }
          }
        ],
        isActive: true
      });

      for (const media of expiredMedia) {
        const fileSize = await this.getFileSize(media.storage.encryptedPath);
        const thumbnailSize = media.storage.thumbnailPath 
          ? await this.getFileSize(media.storage.thumbnailPath) 
          : 0;

        if (!options.dryRun) {
          // Delete files
          await this.deleteFile(media.storage.encryptedPath);
          if (media.storage.thumbnailPath) {
            await this.deleteFile(media.storage.thumbnailPath);
          }

          // Mark as inactive
          media.isActive = false;
          media.metadata.deletedAt = new Date();
          media.metadata.deletionReason = 'expired_cleanup';
          await media.save();
        }

        count++;
        spaceFreed += fileSize + thumbnailSize;
        console.log(`🗑️ Cleaned expired media: ${media.mediaId} (${fileSize + thumbnailSize} bytes)`);
      }

    } catch (error) {
      console.error('❌ Error cleaning expired media:', error);
    }

    return { count, spaceFreed };
  }

  /**
   * Get file size safely
   */
  private async getFileSize(filePath: string): Promise<number> {
    try {
      if (existsSync(filePath)) {
        const stats = await fs.stat(filePath);
        return stats.size;
      }
    } catch (error) {
      console.warn(`⚠️ Could not get size for file: ${filePath}`);
    }
    return 0;
  }

  /**
   * Delete file safely
   */
  private async deleteFile(filePath: string): Promise<void> {
    try {
      if (existsSync(filePath)) {
        await fs.unlink(filePath);
        console.log(`🗑️ Deleted file: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ Failed to delete file: ${filePath}`, error);
    }
  }

  /**
   * Log cleanup results for audit
   */
  private async logCleanupResults(stats: CleanupStats, options: CleanupOptions): Promise<void> {
    try {
      await AuditLogModel.create({
        action: 'media_cleanup',
        userId: 'system',
        details: {
          stats,
          options,
          timestamp: new Date()
        },
        severity: 'low',
        category: 'maintenance'
      });
    } catch (error) {
      console.error('❌ Failed to log cleanup results:', error);
    }
  }

  /**
   * Get cleanup statistics without performing cleanup
   */
  public async getCleanupStats(): Promise<{
    orphanedMediaRecords: number;
    orphanedFiles: number;
    expiredMedia: number;
    totalStorageUsed: number;
    reclaimableSpace: number;
  }> {
    try {
      // Count orphaned media records
      const orphanedMedia = await Media.find({
        messageId: { $exists: true },
        isActive: true
      });

      let orphanedRecords = 0;
      for (const media of orphanedMedia) {
        if (media.messageId) {
          const messageExists = await Message.findById(media.messageId);
          if (!messageExists) {
            orphanedRecords++;
          }
        }
      }

      // Count orphaned files
      let orphanedFiles = 0;
      if (existsSync(this.uploadDir)) {
        const uploadFiles = await fs.readdir(this.uploadDir);
        for (const fileName of uploadFiles) {
          const filePath = path.join(this.uploadDir, fileName);
          const mediaRecord = await Media.findOne({
            'storage.encryptedPath': filePath,
            isActive: true
          });
          if (!mediaRecord) orphanedFiles++;
        }
      }

      // Count expired media
      const cutoffDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
      const expiredMedia = await Media.countDocuments({
        $or: [
          { 'access.expiresAt': { $lt: new Date() } },
          { 
            createdAt: { $lt: cutoffDate },
            'metadata.lastAccessedAt': { $lt: cutoffDate }
          }
        ],
        isActive: true
      });

      // Calculate storage usage
      const allMedia = await Media.find({ isActive: true });
      let totalStorageUsed = 0;
      let reclaimableSpace = 0;

      for (const media of allMedia) {
        const fileSize = await this.getFileSize(media.storage.encryptedPath);
        const thumbnailSize = media.storage.thumbnailPath 
          ? await this.getFileSize(media.storage.thumbnailPath) 
          : 0;
        
        totalStorageUsed += fileSize + thumbnailSize;

        // Check if this media is reclaimable
        if (media.messageId) {
          const messageExists = await Message.findById(media.messageId);
          if (!messageExists) {
            reclaimableSpace += fileSize + thumbnailSize;
          }
        }
      }

      return {
        orphanedMediaRecords: orphanedRecords,
        orphanedFiles,
        expiredMedia,
        totalStorageUsed,
        reclaimableSpace
      };

    } catch (error) {
      console.error('❌ Error getting cleanup stats:', error);
      return {
        orphanedMediaRecords: 0,
        orphanedFiles: 0,
        expiredMedia: 0,
        totalStorageUsed: 0,
        reclaimableSpace: 0
      };
    }
  }

  /**
   * Force cleanup of specific media
   */
  public async forceCleanupMedia(mediaId: string): Promise<boolean> {
    try {
      const media = await Media.findOne({ mediaId, isActive: true });
      if (!media) {
        return false;
      }

      // Delete files
      await this.deleteFile(media.storage.encryptedPath);
      if (media.storage.thumbnailPath) {
        await this.deleteFile(media.storage.thumbnailPath);
      }

      // Mark as inactive
      media.isActive = false;
      media.metadata.deletedAt = new Date();
      media.metadata.deletionReason = 'force_cleanup';
      await media.save();

      console.log(`🗑️ Force cleaned media: ${mediaId}`);
      return true;

    } catch (error) {
      console.error(`❌ Failed to force cleanup media ${mediaId}:`, error);
      return false;
    }
  }
}

// Export singleton instance
export const mediaCleanupService = new MediaCleanupService();
export default mediaCleanupService;
