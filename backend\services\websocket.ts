/**
 * WebSocket Service for Real-time Messaging
 * Replaces polling with instant message delivery
 * No typing indicators, online status, or read receipts as per requirements
 */

import { Server } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import jwt from 'jsonwebtoken';
import UserModel from '../models/User';

interface AuthenticatedWebSocket extends WebSocket {
  userId?: string;
  username?: string;
  isSuperuser?: boolean;
  isAlive?: boolean;
}

interface WebSocketMessage {
  type: 'message' | 'media' | 'system' | 'error' | 'admin_monitor';
  data: any;
  timestamp: number;
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, AuthenticatedWebSocket> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize WebSocket server
   */
  public initialize(server: Server): void {
    this.wss = new WebSocketServer({ 
      server,
      path: '/ws/chat',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startHeartbeat();

    console.log('✅ WebSocket service initialized for real-time messaging');
  }

  /**
   * Verify client authentication
   */
  private async verifyClient(info: any): Promise<boolean> {
    try {
      const url = new URL(info.req.url, 'http://localhost');
      const token = url.searchParams.get('token');

      if (!token) {
        console.log('❌ WebSocket connection rejected: No token provided');
        return false;
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;
      
      // Attach user info to request for later use
      info.req.userId = decoded.userId || decoded.adminId;
      info.req.username = decoded.username;
      info.req.isSuperuser = decoded.isSuperuser || false;

      return true;
    } catch (error) {
      console.log('❌ WebSocket authentication failed:', error);
      return false;
    }
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(ws: AuthenticatedWebSocket, req: any): void {
    const userId = req.userId;
    const username = req.username;
    const isSuperuser = req.isSuperuser;

    // Set up authenticated WebSocket
    ws.userId = userId;
    ws.username = username;
    ws.isSuperuser = isSuperuser;
    ws.isAlive = true;

    // Store client connection
    this.clients.set(userId, ws);

    console.log(`✅ WebSocket connected: ${username} (${userId})`);

    // Set up message handling
ws.on('message', (data) => {
  let buffer: Buffer;
  if (Buffer.isBuffer(data)) {
    buffer = data;
  } else if (typeof data === 'string') {
    buffer = Buffer.from(data);
  } else if (data instanceof ArrayBuffer) {
    buffer = Buffer.from(new Uint8Array(data));
  } else {
    buffer = Buffer.alloc(0);
  }
  this.handleMessage(ws, buffer);
});    ws.on('close', () => this.handleDisconnection(ws));
    ws.on('error', (error) => this.handleError(ws, error));
    ws.on('pong', () => { ws.isAlive = true; });

    // No automatic welcome message as per requirements
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(ws: AuthenticatedWebSocket, data: Buffer): void {
    try {
      const message = JSON.parse(data.toString());
      
      // For now, we only handle system messages
      // Actual message sending still goes through HTTP API for proper encryption/storage
      if (message.type === 'ping') {
        this.sendToClient(ws, {
          type: 'system',
          data: { message: 'pong' },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('❌ Error handling WebSocket message:', error);
      this.sendToClient(ws, {
        type: 'error',
        data: { message: 'Invalid message format' },
        timestamp: Date.now()
      });
    }
  }

  /**
   * Handle WebSocket disconnection
   */
  private handleDisconnection(ws: AuthenticatedWebSocket): void {
    if (ws.userId) {
      this.clients.delete(ws.userId);
      console.log(`❌ WebSocket disconnected: ${ws.username} (${ws.userId})`);
    }
  }

  /**
   * Handle WebSocket errors
   */
  private handleError(ws: AuthenticatedWebSocket, error: Error): void {
    console.error(`❌ WebSocket error for ${ws.username}:`, error);
  }

  /**
   * Send message to specific client
   */
  private sendToClient(ws: AuthenticatedWebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * Broadcast new message to relevant users
   * Called from HTTP API after message is saved to database
   */
  public broadcastNewMessage(senderId: string, recipientId: string, messageData: any): void {
    const message: WebSocketMessage = {
      type: 'message',
      data: messageData,
      timestamp: Date.now()
    };

    // Send to recipient
    const recipientWs = this.clients.get(recipientId);
    if (recipientWs) {
      this.sendToClient(recipientWs, message);
      console.log(`📤 Real-time message sent to ${recipientWs.username}`);
    }

    // Send confirmation to sender (if different from recipient)
    if (senderId !== recipientId) {
      const senderWs = this.clients.get(senderId);
      if (senderWs) {
        this.sendToClient(senderWs, {
          ...message,
          data: { ...messageData, isOwnMessage: true }
        });
      }
    }

    // Send to all connected admin users for real-time monitoring
    this.broadcastToAdmins(message);
  }

  /**
   * Broadcast media attachment notification
   */
  public broadcastMediaMessage(senderId: string, recipientId: string, mediaData: any): void {
    const message: WebSocketMessage = {
      type: 'media',
      data: mediaData,
      timestamp: Date.now()
    };

    // Send to recipient
    const recipientWs = this.clients.get(recipientId);
    if (recipientWs) {
      this.sendToClient(recipientWs, message);
      console.log(`📤 Real-time media message sent to ${recipientWs.username}`);
    }

    // Send confirmation to sender
    if (senderId !== recipientId) {
      const senderWs = this.clients.get(senderId);
      if (senderWs) {
        this.sendToClient(senderWs, {
          ...message,
          data: { ...mediaData, isOwnMessage: true }
        });
      }
    }

    // Send to all connected admin users for real-time monitoring
    this.broadcastToAdmins(message);
  }

  /**
   * Broadcast message to all connected admin users for real-time monitoring
   */
  private broadcastToAdmins(message: WebSocketMessage): void {
    this.clients.forEach((ws) => {
      if (ws.isSuperuser) {
        this.sendToClient(ws, {
          ...message,
          type: 'admin_monitor',
          data: { ...message.data, isAdminMonitor: true }
        });
      }
    });
  }

  /**
   * Get connected users count
   */
  public getConnectedUsersCount(): number {
    return this.clients.size;
  }

  /**
   * Check if user is connected
   */
  public isUserConnected(userId: string): boolean {
    return this.clients.has(userId);
  }

  /**
   * Start heartbeat to detect dead connections
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.clients.forEach((ws, userId) => {
        if (!ws.isAlive) {
          console.log(`💀 Removing dead WebSocket connection: ${userId}`);
          ws.terminate();
          this.clients.delete(userId);
          return;
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // Check every 30 seconds
  }

  /**
   * Cleanup WebSocket service
   */
  public cleanup(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.clients.forEach((ws) => {
      ws.close();
    });

    this.clients.clear();

    if (this.wss) {
      this.wss.close();
    }

    console.log('🧹 WebSocket service cleaned up');
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;
