/**
 * Admin Chat Monitor
 * Allows admin to view chat conversations and messages
 */

import React, { useState, useEffect, useRef } from 'react';

interface ChatMessage {
  id: string;
  chatId: string;
  sender: {
    id: string;
    username: string;
    displayName: string;
    isSuperuser: boolean;
  };
  recipient: {
    id: string;
    username: string;
    displayName: string;
    isSuperuser: boolean;
  };
  content: any;
  sentAt: string;
  platform: string;
  status: string;
  isEncrypted: boolean;
}

interface AdminChatMonitorProps {
  selectedUserId?: string;
}

export const AdminChatMonitor: React.FC<AdminChatMonitorProps> = ({ selectedUserId }) => {
  const [conversations, setConversations] = useState<any[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);

  const fetchConversations = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/chat/conversations', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }

      const data = await response.json();
      setConversations(data.conversations || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async (chatId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/chat/messages/${chatId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch messages');
      }

      const data = await response.json();
      setMessages(data.messages || []);
      setSelectedChatId(chatId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // Initialize WebSocket connection for real-time updates
  const initializeWebSocket = () => {
    const token = localStorage.getItem('adminToken');
    if (!token) return;

    const wsUrl = `ws://10.39.45.162:3000/ws/chat?token=${encodeURIComponent(token)}`;

    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('✅ Admin WebSocket connected for real-time chat monitoring');
        setIsConnected(true);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const wsMessage = JSON.parse(event.data);

          if (wsMessage.type === 'admin_monitor' && wsMessage.data.isAdminMonitor) {
            // Real-time message update for admin monitoring
            const newMessage = wsMessage.data;

            // Update messages if we're viewing the relevant chat
            if (selectedChatId && newMessage.chatId === selectedChatId) {
              setMessages(prev => {
                const exists = prev.some(msg => msg.id === newMessage.id);
                if (exists) return prev;

                return [...prev, newMessage].sort((a, b) =>
                  new Date(a.sentAt).getTime() - new Date(b.sentAt).getTime()
                );
              });
            }

            // Update conversations list to show new activity
            setConversations(prev =>
              prev.map(conv =>
                conv.chatId === newMessage.chatId
                  ? { ...conv, lastMessage: newMessage.content.text, lastMessageAt: newMessage.sentAt }
                  : conv
              )
            );
          }
        } catch (error) {
          console.error('❌ Error parsing admin WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('🔌 Admin WebSocket disconnected');
        setIsConnected(false);

        // Attempt to reconnect after 3 seconds
        setTimeout(() => {
          if (wsRef.current?.readyState === WebSocket.CLOSED) {
            initializeWebSocket();
          }
        }, 3000);
      };

      wsRef.current.onerror = (error) => {
        console.error('❌ Admin WebSocket error:', error);
        setIsConnected(false);
      };

    } catch (error) {
      console.error('❌ Failed to initialize admin WebSocket:', error);
    }
  };

  useEffect(() => {
    fetchConversations();
    initializeWebSocket();

    // Cleanup WebSocket on unmount
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const formatTimestamp = (date: string) => {
    return new Date(date).toLocaleString();
  };

  const fetchAndDecryptMessages = () => {
    if (selectedChatId) {
      fetchMessages(selectedChatId);
    } else {
      fetchConversations();
    }
  };

  const renderMessage = (message: ChatMessage) => {
    const isUser = !message.sender.isSuperuser;
    const content = message.content;

    return (
      <div
        key={message.id}
        className={`message-item ${isUser ? 'user-message' : 'admin-message'}`}
        style={{
          margin: '10px 0',
          padding: '12px',
          borderRadius: '8px',
          backgroundColor: isUser ? '#007AFF' : '#F2F2F7',
          color: isUser ? 'white' : 'black',
          alignSelf: isUser ? 'flex-end' : 'flex-start',
          maxWidth: '70%'
        }}
      >
        <div style={{ marginBottom: '8px' }}>
          <strong>{message.sender.displayName || message.sender.username}</strong>
          {message.isEncrypted && (
            <span style={{ marginLeft: '8px', fontSize: '12px', opacity: 0.7 }}>
              🔐 Encrypted
            </span>
          )}
        </div>

        {/* Text content */}
        {content.text && (
          <div style={{ marginBottom: '8px' }}>
            {content.text}
          </div>
        )}

        {/* Attachment content */}
        {content.attachment && (
          <div style={{ 
            padding: '8px', 
            backgroundColor: 'rgba(0,0,0,0.1)', 
            borderRadius: '4px',
            marginBottom: '8px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: '8px' }}>
                {content.attachment.mimeType?.startsWith('image/') ? '🖼️' :
                 content.attachment.mimeType?.startsWith('video/') ? '🎥' :
                 content.attachment.mimeType?.startsWith('audio/') ? '🎵' : '📎'}
              </span>
              <div>
                <div style={{ fontWeight: 'bold' }}>
                  {content.attachment.originalName}
                </div>
                <div style={{ fontSize: '12px', opacity: 0.7 }}>
                  {content.attachment.mimeType} • {Math.round(content.attachment.size / 1024)}KB
                </div>
              </div>
            </div>
            
            {/* Download link for admin */}
            <a
              href={`/api/admin/chat/attachment/${content.attachment.fileName}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{ 
                color: isUser ? 'white' : '#007AFF',
                fontSize: '12px',
                textDecoration: 'underline',
                marginTop: '4px',
                display: 'block'
              }}
            >
              Download File
            </a>
          </div>
        )}

        <div style={{ fontSize: '11px', opacity: 0.6, textAlign: 'right' }}>
          {formatTimestamp(message.sentAt)}
        </div>
      </div>
    );
  };

  if (loading) {
    return <div style={{ padding: '20px' }}>Loading messages...</div>;
  }

  if (error) {
    return (
      <div style={{ padding: '20px', color: 'red' }}>
        Error: {error}
        <button onClick={fetchAndDecryptMessages} style={{ marginLeft: '10px' }}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h3>Admin Chat Monitor</h3>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          padding: '8px 12px',
          borderRadius: '20px',
          backgroundColor: isConnected ? '#e8f5e8' : '#ffe8e8',
          border: `1px solid ${isConnected ? '#4caf50' : '#f44336'}`
        }}>
          <div style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: isConnected ? '#4caf50' : '#f44336',
            marginRight: '8px'
          }}></div>
          <span style={{
            fontSize: '12px',
            color: isConnected ? '#2e7d32' : '#c62828',
            fontWeight: 'bold'
          }}>
            {isConnected ? 'Real-time Connected' : 'Disconnected'}
          </span>
        </div>
      </div>
      
      {!selectedChatId ? (
        <div>
          <h4>Conversations</h4>
          <div style={{ marginBottom: '20px' }}>
            {conversations.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
                No conversations found
              </div>
            ) : (
              conversations.map((conv) => (
                <div
                  key={conv.chatId}
                  onClick={() => fetchMessages(conv.chatId)}
                  style={{
                    padding: '12px',
                    border: '1px solid #ddd',
                    borderRadius: '8px',
                    margin: '8px 0',
                    cursor: 'pointer',
                    backgroundColor: '#f9f9f9'
                  }}
                >
                  <strong>{conv.participant?.displayName || conv.participant?.username || 'Unknown User'}</strong>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    Last activity: {formatTimestamp(conv.lastActivity)}
                  </div>
                  {conv.messageCount && (
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      Messages: {conv.messageCount}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      ) : (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
            <button
              onClick={() => setSelectedChatId(null)}
              style={{
                marginRight: '10px',
                padding: '8px 16px',
                backgroundColor: '#666',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              ← Back to Conversations
            </button>
            <h4>Chat Messages ({selectedChatId})</h4>
          </div>
          
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px',
            padding: '12px',
            backgroundColor: '#f0f8ff',
            borderRadius: '8px',
            border: '1px solid #b3d9ff'
          }}>
            <p style={{ fontSize: '14px', color: '#0066cc', margin: 0 }}>
              🔐 All encrypted messages are automatically decrypted for admin monitoring
            </p>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {isConnected ? '🟢 Real-time updates active' : '🔴 Real-time updates offline'}
            </div>
          </div>
          
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column',
            height: '600px',
            overflowY: 'auto',
            border: '1px solid #ddd',
            borderRadius: '8px',
            padding: '10px'
          }}>
            {messages.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '50px', color: '#666' }}>
                No messages found
              </div>
            ) : (
              messages.map(renderMessage)
            )}
          </div>

          <button
            onClick={fetchAndDecryptMessages}
            style={{
              marginTop: '10px',
              padding: '8px 16px',
              backgroundColor: '#007AFF',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh Messages
          </button>
        </div>
      )}
    </div>
  );
};
