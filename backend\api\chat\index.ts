import { Request, Response, Router } from 'express';
import { auth, requireUser } from '../../middleware/auth';
import { Chat, Message } from '../../models/Chat';
import User from '../../models/User';
import Media from '../../models/Media';
import { encryptMessage, decryptMessage } from '../../utils/encryption';
import securityLogger from '../../utils/security-logger';
import Joi from 'joi';
import validate from '../../middleware/validate';
import crypto from 'crypto';
import mongoose from 'mongoose';

const router = Router();

// Validation schemas
const sendMessageSchema = Joi.object({
  recipientId: Joi.string().required(),
  content: Joi.string().required().max(10000),
  messageType: Joi.string().valid('text', 'media', 'system', 'ephemeral').default('text'),
  mediaAttachments: Joi.array().items(Joi.string()).max(5).optional(),
  expiresIn: Joi.number().integer().min(1).max(86400).optional(), // seconds
});

const getChatSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(50),
  messageType: Joi.string().valid('text', 'media', 'system', 'ephemeral').optional(),
});

const markReadSchema = Joi.object({
  messageIds: Joi.array().items(Joi.string()).required(),
});

/**
 * Send an end-to-end encrypted message
 * POST /api/chat/send
 */
export async function sendMessage(req: Request, res: Response): Promise<void> {
  try {
    const { recipientId, content, messageType, mediaAttachments, expiresIn } = req.body;
    const senderId = req.user!._id;
    const deviceFingerprint = req.headers['x-device-fingerprint'] as string || 'unknown';
    const bleUUID = req.headers['x-ble-uuid'] as string || 'unknown';

    // Validate recipient using superuser architecture
    const recipientValidation = await validateMessageRecipient((senderId as any).toString(), recipientId);
    if (!recipientValidation.valid) {
      res.status(403).json({ error: recipientValidation.error });
      return;
    }

    // Validate recipient
    const recipient = await User.findById(recipientId);
    if (!recipient) {
      res.status(404).json({ error: 'Recipient not found' });
      return;
    }

    if (recipient.status !== 'active') {
      res.status(400).json({ error: 'Recipient is not active' });
      return;
    }

    // Find or create chat
    let chat = await Chat.findOne({
      participants: { $all: [senderId, recipientId] },
      chatType: 'direct',
      isActive: true,
    });

    if (!chat) {
      // Create new chat
      const chatEncryptionKey = crypto.randomBytes(32).toString('base64');
      
      chat = new Chat({
        participants: [senderId, recipientId],
        messages: [],
        chatType: 'direct',
        encryptionKey: chatEncryptionKey,
        lastActivity: new Date(),
        metadata: {
          createdBy: senderId,
          superuserChat: req.user!.isSuperuser || recipient.isSuperuser,
        },
      });

      await chat.save();
    }

    // Generate encryption parameters
    const encryptionKey = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);
    const salt = crypto.randomBytes(32);    // Encrypt message content
    const encryptedContent = await encryptMessage(content, encryptionKey.toString('hex'));

    // Handle media attachments
    let mediaAttachment;
    if (mediaAttachments && mediaAttachments.length > 0) {
      // Validate media exists and belongs to sender
      const media = await Media.findOne({
        mediaId: mediaAttachments[0], // For now, support single attachment
        uploaderId: senderId,
        isActive: true,
      });

      if (media) {
        mediaAttachment = {
          filename: media.file.originalName,
          encryptedPath: media.storage.encryptedPath,
          mimeType: media.file.mimeType,
          size: media.file.size,
          thumbnailPath: media.storage.thumbnailPath,
        };

        // Update media to associate with message
        media.messageId = undefined; // Will be set after message creation
        await media.save();
      }
    }

    // Create message
    const message = new Message({
      senderId,
      recipientId,
      content: {
        encrypted: encryptedContent,
        iv: iv.toString('base64'),
        salt: salt.toString('base64'),
      },
      messageType,
      mediaAttachment,
      status: 'sent',
      expiresAt: expiresIn ? new Date(Date.now() + expiresIn * 1000) : undefined,
      deviceFingerprint,
      bleUUID,
    });

    await message.save();

    // Update media with message ID
    if (mediaAttachments && mediaAttachments.length > 0) {
      await Media.updateOne(
        { mediaId: mediaAttachments[0] },
        { messageId: message._id }
      );
    }

    // Add message to chat
    chat.messages.push(message._id as any);
    chat.lastActivity = new Date();
    await chat.save();

    // Handle ephemeral messages for superuser
    if (messageType === 'ephemeral' && recipient.isSuperuser) {
      // Set auto-deletion after read
      setTimeout(async () => {
        try {
          const msg = await Message.findById(message._id);
          if (msg && msg.readAt) {
            msg.status = 'expired';
            msg.deletedAt = new Date();
            await msg.save();
          }
        } catch (error) {
          console.error('Failed to auto-delete ephemeral message:', error);
        }
      }, 30000); // 30 seconds after being read
    }

    // Prepare response (exclude sensitive data)
    const responseMessage = {
      messageId: message._id,
      recipientId,
      messageType,
      status: message.status,
      sentAt: message.createdAt,
      expiresAt: message.expiresAt,
      hasAttachment: !!mediaAttachment,
    };    // Log message send
    securityLogger.logDataAccess(
      'media_upload',
      'message_send',
      senderId as string,
      req,
      'success',
      { messageType, hasAttachment: !!mediaAttachment, recipientId }
    );

    res.status(201).json({
      message: 'Message sent successfully',
      data: responseMessage,
    });

  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
}

/**
 * Get chat messages with pagination and decryption
 * GET /api/chat/:chatId/messages
 */
export async function getChatMessages(req: Request, res: Response): Promise<void> {
  try {
    const { chatId } = req.params;
    const { page, limit, messageType } = req.query;
    const userId = req.user!._id;

    // Validate chat exists and user has access
    const chat = await Chat.findOne({
      _id: chatId,
      participants: userId,
      isActive: true,
    });

    if (!chat) {
      res.status(404).json({ error: 'Chat not found or access denied' });
      return;
    }

    // Build message query
    const messageQuery: any = {
      $or: [
        { senderId: userId },
        { recipientId: userId },
      ],
      status: { $ne: 'deleted' },
    };

    if (messageType) {
      messageQuery.messageType = messageType;
    }

    // Handle expired messages
    messageQuery.$or.push({
      expiresAt: { $exists: false }
    }, {
      expiresAt: { $gt: new Date() }
    });

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const [messages, total] = await Promise.all([
      Message.find(messageQuery)
        .populate('senderId', 'username profile.displayName')
        .populate('recipientId', 'username profile.displayName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum),
      Message.countDocuments(messageQuery)
    ]);

    // Decrypt messages for response
    const decryptedMessages = await Promise.all(
      messages.map(async (message) => {
        try {
          // For demo purposes, we'll return a simplified decryption          // In production, you'd need proper key management
          const decryptedContent = await decryptMessage(
            { 
              encrypted: message.content.encrypted, 
              iv: message.content.iv, 
              tag: message.content.salt // Using salt as tag for now
            },
            Buffer.from(message.content.salt, 'base64').toString('hex') // Simplified key derivation
          );

          return {
            messageId: message._id,
            senderId: message.senderId,
            recipientId: message.recipientId,
            content: decryptedContent,
            messageType: message.messageType,
            mediaAttachment: message.mediaAttachment,
            status: message.status,
            sentAt: message.createdAt,
            deliveredAt: message.deliveredAt,
            readAt: message.readAt,
            expiresAt: message.expiresAt,
          };
        } catch (decryptError) {
          console.error('Failed to decrypt message:', decryptError);
          return {
            messageId: message._id,
            error: 'Failed to decrypt message',        sentAt: message.createdAt,
          };
        }
      })
    );

    // Log chat access
    securityLogger.logDataAccess(
      'chat',
      'chat_access',
      userId as string,
      req,
      'success',
      { chatId, messageCount: messages.length }
    );

    res.status(200).json({
      messages: decryptedMessages,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum),
      },
      chat: {
        chatId: chat._id,
        chatType: chat.chatType,
        participants: chat.participants,
        lastActivity: chat.lastActivity,
      },
    });

  } catch (error) {
    console.error('Get chat messages error:', error);
    res.status(500).json({ error: 'Failed to retrieve messages' });
  }
}

/**
 * Mark messages as read
 * POST /api/chat/mark-read
 */
export async function markMessagesRead(req: Request, res: Response): Promise<void> {
  try {
    const { messageIds } = req.body;
    const userId = req.user!._id;

    // Update messages where user is recipient
    const result = await Message.updateMany(
      {
        _id: { $in: messageIds },
        recipientId: userId,
        status: { $in: ['sent', 'delivered'] },
      },
      {
        status: 'read',
        readAt: new Date(),
      }
    );

    // Handle ephemeral message cleanup
    const ephemeralMessages = await Message.find({
      _id: { $in: messageIds },
      messageType: 'ephemeral',
      senderId: { $ne: userId }, // Messages sent TO this user
    });

    for (const message of ephemeralMessages) {
      // Check if sender is superuser
      const sender = await User.findById(message.senderId);
      if (sender?.isSuperuser) {
        // Schedule deletion after brief delay
        setTimeout(async () => {
          try {
            await Message.findByIdAndUpdate(message._id, {
              status: 'expired',
              deletedAt: new Date(),
            });
          } catch (error) {
            console.error('Failed to delete ephemeral message:', error);
          }
        }, 5000); // 5 seconds delay
      }
    }

    res.status(200).json({
      message: 'Messages marked as read',
      updated: result.modifiedCount,
    });

  } catch (error) {
    console.error('Mark messages read error:', error);
    res.status(500).json({ error: 'Failed to mark messages as read' });
  }
}

/**
 * Get user's chat list
 * GET /api/chat/list
 */
export async function getChatList(req: Request, res: Response): Promise<void> {
  try {
    const userId = req.user!._id;
    const user = await User.findById(userId);
    
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    let chats;
    
    if (user.isSuperuser) {
      // Superuser sees all chats from all users
      chats = await Chat.find({
        isActive: true,
        chatType: 'direct'
      })
      .populate('participants', 'username profile.displayName')
      .populate({
        path: 'messages',
        options: { sort: { createdAt: -1 }, limit: 1 },
        select: 'content sender createdAt messageType'
      })
      .sort({ lastActivity: -1 });

      // Group chats by non-superuser participant for superuser view
      const groupedChats: any[] = [];
      chats.forEach(chat => {
        const nonSuperuserParticipant = chat.participants.find((p: any) => 
          p._id.toString() !== (userId as any).toString()
        );
        
        if (nonSuperuserParticipant) {
          groupedChats.push({
            chatId: chat._id,
            participant: {
              id: (nonSuperuserParticipant as any)._id,
              username: (nonSuperuserParticipant as any).username,
              displayName: (nonSuperuserParticipant as any).profile?.displayName || (nonSuperuserParticipant as any).username
            },
            lastMessage: chat.messages[0] || null,
            lastActivity: chat.lastActivity,
            unreadCount: 0 // TODO: Implement unread count logic
          });
        }
      });

      res.json({
        success: true,
        data: {
          chats: groupedChats,
          userType: 'superuser',
          totalChats: groupedChats.length
        }
      });

    } else {
      // Regular users only see superuser in their chat list
      const superuser = await User.findOne({ isSuperuser: true, status: 'active' });
      
      if (!superuser) {
        res.json({
          success: true,
          data: {
            chats: [],
            userType: 'regular',
            message: 'No superuser available for chat'
          }
        });
        return;
      }

      // Find or create chat with superuser
      let chat = await Chat.findOne({
        participants: { $all: [userId, superuser._id] },
        chatType: 'direct',
        isActive: true
      })
      .populate({
        path: 'messages',
        options: { sort: { createdAt: -1 }, limit: 1 },
        select: 'content sender createdAt messageType'
      });

      if (!chat) {
        // Create chat with superuser if it doesn't exist
        const chatEncryptionKey = crypto.randomBytes(32).toString('base64');
        
        chat = new Chat({
          participants: [userId, superuser._id],
          messages: [],
          chatType: 'direct',
          encryptionKey: chatEncryptionKey,
          lastActivity: new Date(),
          metadata: {
            createdBy: userId,
            superuserChat: true,
          },
        });

        await chat.save();
      }

      res.json({
        success: true,
        data: {
          chats: [{
            chatId: chat._id,
            participant: {
              id: superuser._id,
              username: superuser.username,
              displayName: superuser.profile.displayName,
              isSuperuser: true
            },
            lastMessage: chat.messages[0] || null,
            lastActivity: chat.lastActivity,
            unreadCount: 0 // TODO: Implement unread count logic
          }],
          userType: 'regular',
          totalChats: 1
        }
      });
    }

  } catch (error) {
    console.error('Error getting chat list:', error);
    res.status(500).json({ 
      error: 'Failed to get chat list',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Delete a message (soft delete)
 * DELETE /api/chat/message/:messageId
 */
export async function deleteMessage(req: Request, res: Response): Promise<void> {
  try {
    const { messageId } = req.params;
    const userId = req.user!._id;

    const message = await Message.findById(messageId);
    if (!message) {
      res.status(404).json({ error: 'Message not found' });
      return;
    }

    // Check if user can delete (sender or superuser)
    if (message.senderId.toString() !== (userId as any).toString() && !req.user!.isSuperuser) {
      res.status(403).json({ error: 'Access denied' });
      return;
    }

    // Soft delete    message.status = 'deleted';
    message.deletedAt = new Date();
    await message.save();

    // Log deletion
    securityLogger.logDataAccess(
      'message',
      'message_delete',
      userId as string,
      req,
      'success',
      { messageId, messageType: message.messageType }
    );

    res.status(200).json({ message: 'Message deleted successfully' });

  } catch (error) {
    console.error('Delete message error:', error);
    res.status(500).json({ error: 'Failed to delete message' });
  }
}

/**
 * Get message delivery status
 * GET /api/chat/message/:messageId/status
 */
export async function getMessageStatus(req: Request, res: Response): Promise<void> {
  try {
    const { messageId } = req.params;
    const userId = req.user!._id;

    const message = await Message.findById(messageId);
    if (!message) {
      res.status(404).json({ error: 'Message not found' });
      return;
    }    // Check if user can access (sender or recipient)
    if (message.senderId.toString() !== (userId as any).toString() &&
        message.recipientId.toString() !== (userId as any).toString()) {
      res.status(403).json({ error: 'Access denied' });
      return;
    }

    res.status(200).json({
      messageId: message._id,
      status: message.status,
      sentAt: message.createdAt,
      deliveredAt: message.deliveredAt,
      readAt: message.readAt,
      expiresAt: message.expiresAt,
    });

  } catch (error) {
    console.error('Get message status error:', error);
    res.status(500).json({ error: 'Failed to get message status' });
  }
}

/**
 * Validate message recipient according to superuser architecture
 * Regular users can only message superuser, superuser can message anyone
 */
async function validateMessageRecipient(senderId: string, recipientId: string): Promise<{ valid: boolean; error?: string }> {
  try {
    const sender = await User.findById(senderId);
    const recipient = await User.findById(recipientId);

    if (!sender || !recipient) {
      return { valid: false, error: 'Invalid sender or recipient' };
    }

    // Superuser can message anyone
    if (sender.isSuperuser) {
      return { valid: true };
    }

    // Regular users can only message superuser
    if (!recipient.isSuperuser) {
      return { valid: false, error: 'You can only send messages to administrators' };
    }

    return { valid: true };
  } catch (error) {
    console.error('Recipient validation error:', error);
    return { valid: false, error: 'Validation failed' };
  }
}

/**
 * Get chat details
 * GET /api/chat/:chatId
 */
export async function getChat(req: Request, res: Response): Promise<void> {
  try {
    const { page = 1, limit = 50, messageType } = req.query;
    const { chatId } = req.params;
    const userId = req.user!._id;

    const chat = await Chat.findById(chatId);
    if (!chat) {
      res.status(404).json({ error: 'Chat not found' });
      return;
    }

    // Verify user is participant
    if (!chat.participants.some(p => p.toString() === (userId as any).toString())) {
      res.status(403).json({ error: 'Access denied' });
      return;
    }

    // Build message query
    const messageQuery: any = { chatId: chat._id };
    if (messageType) {
      messageQuery.messageType = messageType;
    }

    const skip = (Number(page) - 1) * Number(limit);
    const messages = await Message.find(messageQuery)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit))
      .populate('senderId', 'username profile.displayName')
      .lean();

    // Decrypt messages for response
    const decryptedMessages = messages.map(message => ({
      ...message,
      content: message.content.encrypted ? 
        decryptMessage(
          {
            encrypted: message.content.encrypted,
            iv: message.content.iv,
            tag: message.content.salt // Using salt as tag for compatibility
          },
          process.env.ENCRYPTION_KEY || 'default-key'
        ) : message.content
    }));

    res.status(200).json({
      chat: {
        id: chat._id,
        participants: chat.participants,
        lastActivity: chat.lastActivity,
        messageCount: chat.messages.length
      },
      messages: decryptedMessages.reverse(),
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: await Message.countDocuments(messageQuery)
      }
    });

  } catch (error) {
    console.error('Get chat error:', error);
    res.status(500).json({ error: 'Failed to get chat' });
  }
}

// Import mobile routes
import mobileRouter from './mobile';
import unifiedRouter from './unified';

// Configure routes
router.post('/send', auth, requireUser, validate(sendMessageSchema), sendMessage);
router.get('/list', auth, requireUser, getChatList);
router.get('/:chatId', auth, requireUser, getChat);
router.post('/mark-read', auth, requireUser, validate(markReadSchema), markMessagesRead);
router.delete('/message/:messageId', auth, requireUser, deleteMessage);
router.get('/message/:messageId/status', auth, requireUser, getMessageStatus);

// Mount mobile routes (legacy - will be deprecated)
router.use('/mobile', mobileRouter);

// Mount unified chat routes (fixes media attachment issues)
router.use('/unified', unifiedRouter);

export default router;
