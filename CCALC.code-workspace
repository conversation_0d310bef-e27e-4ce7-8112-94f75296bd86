{"folders": [{"name": "🏗️ Backend", "path": "./backend"}, {"name": "🖥️ Frontend", "path": "./frontend"}, {"name": "📱 Mobile App", "path": "./app"}, {"name": "📊 Project Root", "path": "."}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true, "**/build": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true, "**/build": true}}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🚀 Start Backend Dev", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "🖥️ Start Frontend Dev", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "📱 Start React Native Metro", "type": "shell", "command": "npm start", "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "📱 Start Expo for iPhone", "type": "shell", "command": "npx expo start", "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "📡 Test Backend from iPhone", "type": "shell", "command": "curl -s http://************:3000/health && echo '\n✅ Backend accessible from iPhone'", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}}, {"label": "🍎 Build for iOS", "type": "shell", "command": "npm run ios", "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🧪 Backend Build Test", "type": "shell", "command": "npm run build", "options": {"cwd": "${workspaceFolder}/backend"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "🐳 Docker Up", "type": "shell", "command": "docker-compose up -d", "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}}, {"label": "🐳 Docker Down", "type": "shell", "command": "docker-compose down", "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}}]}, "launch": {"version": "0.2.0", "configurations": [{"name": "🚀 Debug Backend", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/server.ts", "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"], "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development", "PORT": "3000"}, "console": "integratedTerminal", "restart": true, "protocol": "inspector", "cwd": "${workspaceFolder}/backend"}, {"name": "🧪 Debug Backend Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/node_modules/.bin/jest", "args": ["--runInBand"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "cwd": "${workspaceFolder}/backend"}]}, "extensions": {"recommendations": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-json", "ms-vscode.extension-browser", "ms-docker.docker", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-eslint", "esbenp.prettier-vscode"]}}