/**
 * Admin Chat Management API
 * Allows admin to view and decrypt chat messages
 */

import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import { adminPanelOnly } from '../../middleware/strictAuth';
import { MobileChat, MobileMessage } from '../../models/MobileChat';
import UserModel from '../../models/User';
import AuditLogModel from '../../models/AuditLog';
import { ChatEncryption } from '../../utils/encryption';
import path from 'path';
import fs from 'fs/promises';

const router = Router();

/**
 * Get all chat conversations for admin panel
 * GET /api/admin/chat/conversations
 */
router.get('/conversations', authenticateToken, adminPanelOnly, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).user?.id;

    console.log('👑 Admin chat conversations request:', { adminId });

    // Get all active chats
    const chats = await MobileChat.find({ isActive: true })
      .populate('participants', 'username displayName status')
      .sort({ 'metadata.lastActivity': -1 });

    const conversations = await Promise.all(
      chats.map(async (chat) => {
        // Get latest message
        const latestMessage = await MobileMessage.findOne({ chatId: chat.chatId })
          .sort({ 'metadata.sentAt': -1 });

        // Get participant info
        const participants = chat.participants as any[];
        const nonSuperuserParticipant = participants.find(p => !p.isSuperuser);

        return {
          chatId: chat.chatId,
          participant: nonSuperuserParticipant ? {
            id: nonSuperuserParticipant._id,
            username: nonSuperuserParticipant.username,
            displayName: nonSuperuserParticipant.displayName,
            status: nonSuperuserParticipant.status
          } : null,
          lastActivity: chat.metadata.lastActivity,
          messageCount: chat.metadata.messageCount,
          lastMessage: latestMessage ? {
            content: latestMessage.content,
            sentAt: latestMessage.metadata.sentAt,
            isEncrypted: latestMessage.isEncrypted
          } : null
        };
      })
    );

    // Log admin access
    await AuditLogModel.create({
      logId: `admin_chat_access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId,
      event: {
        type: 'admin_action',
        action: 'CHAT_CONVERSATIONS_ACCESSED',
        resource: '/api/admin/chat/conversations',
        result: 'success',
        severity: 'low'
      },
      context: {
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 20,
        authMethod: 'password',
        mfaUsed: false,
        suspiciousActivity: false
      },
      data: {
        metadata: {
          conversationCount: conversations.length
        }
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      }
    });

    res.json({
      success: true,
      conversations
    });

  } catch (error: any) {
    console.error('Admin chat conversations error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversations'
    });
  }
});

/**
 * Get messages for a specific chat
 * GET /api/admin/chat/messages/:chatId
 */
router.get('/messages/:chatId', authenticateToken, adminPanelOnly, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).user?.id;
    const { chatId } = req.params;
    const { limit = 100, offset = 0 } = req.query;

    console.log('👑 Admin chat messages request:', { adminId, chatId, limit, offset });

    // Verify chat exists
    const chat = await MobileChat.findOne({ chatId });
    if (!chat) {
      res.status(404).json({
        success: false,
        error: 'Chat not found'
      });
      return;
    }

    // Get messages
    const messages = await MobileMessage.find({ chatId })
      .sort({ 'metadata.sentAt': -1 })
      .limit(Number(limit))
      .skip(Number(offset))
      .populate('senderId', 'username displayName isSuperuser')
      .populate('recipientId', 'username displayName isSuperuser');

    // Format messages for admin (with decryption)
    const formattedMessages = await Promise.all(
      messages.map(async (msg) => {
        const sender = msg.senderId as any;
        const recipient = msg.recipientId as any;

        // Decrypt message content if encrypted using E2E admin backdoor
        let decryptedText = msg.content.text;
        if (
          msg.isEncrypted &&
          typeof msg.content === 'object' &&
          'adminBackdoor' in msg.content &&
          msg.content.adminBackdoor
        ) {
          try {
            const E2EEncryption = require('../../services/e2e-encryption').E2EEncryptionService;
            const e2eService = new E2EEncryption();
            decryptedText = await e2eService.decryptAdminBackdoor(msg.content.adminBackdoor);
            console.log('✅ Admin decrypted E2E message successfully');
          } catch (error) {
            console.warn('Admin E2E decryption failed:', error);

            // Fallback to legacy decryption
            try {
              const userId = 'mobileuser';
              const user = await UserModel.findOne({ username: userId });
              const deviceFingerprint = user?.deviceFingerprintHash || 'unknown';

              decryptedText = await ChatEncryption.tryDecryptMessage(
                decryptedText,
                userId,
                deviceFingerprint
              );
              console.log('✅ Admin decrypted using legacy method');
            } catch (legacyError) {
              console.warn('Legacy admin decryption also failed:', legacyError);
              decryptedText = '[ENCRYPTED MESSAGE - Decryption Failed]';
            }
          }
        }

        return {
          id: msg.messageId,
          chatId: msg.chatId,
          sender: {
            id: sender._id,
            username: sender.username,
            displayName: sender.displayName,
            isSuperuser: sender.isSuperuser
          },
          recipient: {
            id: recipient._id,
            username: recipient.username,
            displayName: recipient.displayName,
            isSuperuser: recipient.isSuperuser
          },
          content: {
            ...msg.content,
            text: decryptedText,
            originalText: msg.isEncrypted ? msg.content.text : undefined // Keep original encrypted text for reference
          },
          sentAt: msg.metadata.sentAt,
          platform: msg.metadata.platform,
          status: msg.status,
          isEncrypted: msg.isEncrypted
        };
      })
    );

    // Log admin access
    await AuditLogModel.create({
      logId: `admin_chat_messages_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId,
      event: {
        type: 'admin_action',
        action: 'CHAT_MESSAGES_ACCESSED',
        resource: `/api/admin/chat/messages/${chatId}`,
        result: 'success',
        severity: 'medium'
      },
      context: {
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 30,
        authMethod: 'password',
        mfaUsed: false,
        suspiciousActivity: false
      },
      data: {
        metadata: {
          chatId,
          messageCount: formattedMessages.length,
          participantIds: chat.participants
        }
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      }
    });

    res.json({
      success: true,
      messages: formattedMessages.reverse(), // Chronological order
      chat: {
        chatId: chat.chatId,
        participants: chat.participants,
        messageCount: chat.metadata.messageCount,
        lastActivity: chat.metadata.lastActivity
      }
    });

  } catch (error: any) {
    console.error('Admin chat messages error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch messages'
    });
  }
});

/**
 * Download attachment file (admin only)
 * GET /api/admin/chat/attachment/:fileName
 */
router.get('/attachment/:fileName', authenticateToken, adminPanelOnly, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).user?.id;
    const { fileName } = req.params;

    console.log('👑 Admin attachment download:', { adminId, fileName });

    // Security check - ensure filename is safe
    if (!fileName || fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      res.status(400).json({
        success: false,
        error: 'Invalid filename'
      });
      return;
    }

    const filePath = path.join(process.cwd(), 'uploads', 'chat-attachments', fileName);

    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      res.status(404).json({
        success: false,
        error: 'File not found'
      });
      return;
    }

    // Log admin file access
    await AuditLogModel.create({
      logId: `admin_file_access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId,
      event: {
        type: 'admin_action',
        action: 'CHAT_ATTACHMENT_ACCESSED',
        resource: `/api/admin/chat/attachment/${fileName}`,
        result: 'success',
        severity: 'medium'
      },
      context: {
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 40,
        authMethod: 'password',
        mfaUsed: false,
        suspiciousActivity: false
      },
      data: {
        metadata: {
          fileName,
          filePath: filePath
        }
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: true,
        exportable: false
      }
    });

    // Send file
    res.sendFile(filePath);

  } catch (error: any) {
    console.error('Admin attachment download error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download attachment'
    });
  }
});

export default router;
