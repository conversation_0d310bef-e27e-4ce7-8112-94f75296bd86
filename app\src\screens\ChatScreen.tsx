/**
 * iOS Chat Screen Component
 * WhatsApp-like instant media preview with persistent upload indicators
 * Features: Swipe-to-reply, upload progress, retry functionality
 * Optimized for iPhone experience
 */

import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
  Animated,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import { theme } from '../utils/theme';
import { chatStyles, chatColors } from '../styles/ChatStyles';
import { AuthService } from '../services/AuthService';
import { ChatService, ChatMessage } from '../services/ChatService';
import WebSocketService from '../services/WebSocketService';
import { ChatListItem } from './ChatListScreen';
import { MediaAttachment } from '../services/MediaService';
import { MediaCacheManager } from '../services/MediaCacheManager';
import { AttachmentPicker } from '../components/AttachmentPicker';
import { ImageViewer } from '../components/ImageViewer';
import { SwipeableMessage } from '../components/SwipeableMessage';
import { UploadProgressOverlay } from '../components/UploadProgressOverlay';
import { ErrorBanner } from '../components/ErrorBanner';
import { FadeInMedia } from '../components/FadeInMedia';

interface ChatScreenProps {
  onLogout: () => void;
  onBack: () => void;
  chatUser?: ChatListItem;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ onLogout, onBack, chatUser }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAttachment, setSelectedAttachment] = useState<MediaAttachment | null>(null);
  const [showAttachmentPicker, setShowAttachmentPicker] = useState(false);
  const [superuserName, setSuperuserName] = useState('Support');
  const [selectedImage, setSelectedImage] = useState<{ visible: boolean; uri: string }>({
    visible: false,
    uri: '',
  });
  const [replyToMessage, setReplyToMessage] = useState<ChatMessage | null>(null);
  const [errorBanner, setErrorBanner] = useState<{ message: string; onRetry?: () => void } | null>(null);
  // --- Retry Cooldown State ---
  const [retryCooldowns, setRetryCooldowns] = useState<{ [attachmentId: string]: number }>({});
  
  const flatListRef = useRef<FlatList>(null);
  const chatService = ChatService.getInstance();
  const authService = AuthService.getInstance();
  const webSocketService = new WebSocketService(authService);
  const mediaCacheManager = MediaCacheManager.getInstance();

  // Utility function to parse reply information from message text
  const parseReplyInfo = (text: string) => {
    const replyMatch = text.match(/^@([a-f0-9-]+)\s+(.*)$/);
    if (replyMatch) {
      return {
        replyToId: replyMatch[1],
        actualText: replyMatch[2],
        isReply: true
      };
    }
    return {
      replyToId: null,
      actualText: text,
      isReply: false
    };
  };

  // Find the original message being replied to
  const findReplyToMessage = (replyToId: string): ChatMessage | null => {
    return messages.find(msg => msg.id === replyToId) || null;
  };

  // Initialize chat connection with WebSocket
  useEffect(() => {
    initializeChat();

    // Set up WebSocket for real-time messaging
    webSocketService.onMessage((newMessage) => {
      console.log('📨 Real-time message received:', newMessage);
      setMessages(prev => {
        // Avoid duplicates
        const exists = prev.some(msg => msg.id === newMessage.id);
        if (exists) return prev;

        return [...prev, newMessage].sort((a, b) => a.timestamp - b.timestamp);
      });
    });

    webSocketService.onConnection((connected) => {
      setIsConnected(connected);
      console.log(connected ? '✅ Real-time messaging connected' : '❌ Real-time messaging disconnected');
    });

    webSocketService.onError((error) => {
      console.error('❌ WebSocket error:', error);
      setErrorBanner({
        message: 'Real-time messaging unavailable',
        onRetry: () => {
          setErrorBanner(null);
          webSocketService.connect();
        }
      });
    });

    // Connect to WebSocket
    webSocketService.connect();

    // Fallback status check (less frequent since we have real-time updates)
    const statusInterval = setInterval(() => {
      updateChatStatus();
    }, 30000); // Check every 30 seconds as fallback

    return () => {
      clearInterval(statusInterval);
      webSocketService.disconnect();
    };
  }, []);

  const initializeChat = async () => {
    try {
      setIsLoading(true);
      await loadMessages();
      await updateChatStatus();
    } catch (error) {
      console.error('Failed to initialize chat:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadMessages = async () => {
    try {
      const result = await chatService.getMessages(50, 0);
      if (result.success && result.messages) {
        setMessages(result.messages.sort((a, b) => a.timestamp - b.timestamp));
        
        // Scroll to bottom after loading messages
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } else {
        console.error('Failed to load messages:', result.error);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };
  const updateChatStatus = async () => {
    try {
      const status = await chatService.getChatStatus();
      setIsConnected(status.isConnected);
      // Note: No longer tracking superuser online status as per requirements
    } catch (error) {
      console.error('Error updating chat status:', error);
    }
  };
  // No automatic demo messages as per requirements - chat starts empty
  useEffect(() => {
    setIsConnected(true);
    // Note: No longer tracking superuser online status as per requirements
  }, []);

  // Handle attachment selection
  const handleAttachment = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowAttachmentPicker(true);
  }, []);

  // Handle attachment selection from picker
  const handleAttachmentSelect = useCallback(async (attachment: MediaAttachment) => {
    try {
      // Cache media immediately for instant preview
      if (attachment.isImage || attachment.isVideo) {
        console.log('🔄 Caching media for instant preview:', attachment.name);
        const cachedAttachment = await mediaCacheManager.cacheMediaForInstantPreview(attachment);
        setSelectedAttachment(cachedAttachment);
        console.log('✅ Media cached successfully:', cachedAttachment.id);
      } else {
        setSelectedAttachment(attachment);
      }
    } catch (error) {
      console.error('❌ Failed to cache media:', error);
      // Still use attachment but show warning
      setSelectedAttachment(attachment);
      setErrorBanner({
        message: 'Media preview unavailable, but file will still be sent.',
        onRetry: undefined
      });
    }
    setShowAttachmentPicker(false);
  }, [mediaCacheManager]);

  // Remove selected attachment
  const removeAttachment = useCallback(() => {
    setSelectedAttachment(null);
  }, []);

  const sendMessage = useCallback(async () => {
    if ((!inputText.trim() && !selectedAttachment) || !isConnected) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const messageId = Date.now().toString();
    // Handle reply formatting - WhatsApp-like
    let messageText = inputText.trim() || '';
    
    // Ensure there's always a text value for media messages to satisfy backend validation
    if (selectedAttachment) {
      if (messageText === '') {
        if (selectedAttachment.isImage) {
          messageText = '📷 Image';
        } else if (selectedAttachment.isVideo) {
          messageText = '🎥 Video';
        } else if (selectedAttachment.isAudio) {
          messageText = '🎵 Audio';
        } else {
          messageText = `📎 ${selectedAttachment.name}`;
        }
      }
    }
    
    if (replyToMessage) {
      messageText = `@${replyToMessage.id} ${messageText}`;
    }
    
    const newMessage: ChatMessage = {
      id: messageId,
      chatId: 'default-chat', // Using default chat ID
      text: messageText,
      sender: 'user',
      timestamp: Date.now(),
      status: 'sent',
      isDelivered: false,
      isRead: false,
      attachment: selectedAttachment ? {
        id: selectedAttachment.id,
        name: selectedAttachment.name,
        type: selectedAttachment.type,
        size: selectedAttachment.size,
        uri: selectedAttachment.uri,
        mimeType: selectedAttachment.mimeType,
        isImage: selectedAttachment.isImage,
        isVideo: selectedAttachment.isVideo,
        isAudio: selectedAttachment.isAudio,
      } : undefined,
    };

    // Add message to local state immediately
    setMessages(prev => [...prev, newMessage]);
    setInputText('');
    setSelectedAttachment(null);
    setReplyToMessage(null); // Clear reply after sending

    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Cache media for instant preview if it's an image/video and not already cached
      let attachmentToSend = selectedAttachment;
      if (selectedAttachment && (selectedAttachment.isImage || selectedAttachment.isVideo) && !selectedAttachment.id) {
        console.log('🔄 Caching media before send:', selectedAttachment.name);
        attachmentToSend = await mediaCacheManager.cacheMediaForInstantPreview(selectedAttachment);
        
        // Update the message with cached attachment info
        newMessage.attachment = {
          ...attachmentToSend,
          id: attachmentToSend.id,
          name: attachmentToSend.name,
          type: attachmentToSend.type,
          size: attachmentToSend.size,
          uri: attachmentToSend.uri,
          mimeType: attachmentToSend.mimeType,
          isImage: attachmentToSend.isImage,
          isVideo: attachmentToSend.isVideo,
          isAudio: attachmentToSend.isAudio,
        };
        
        // Update messages with cached attachment
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, attachment: newMessage.attachment }
            : msg
        ));
      }

      // Use ChatService to send message with attachment support
      const result = await chatService.sendMessage(newMessage.text, attachmentToSend || undefined);

      if (result.success && result.message) {
        // Update message status
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, isDelivered: true, id: result.message!.id }
            : msg
        ));
        
        // Removed automated superuser response - let superuser respond naturally
      } else {
        // Handle send failure - keep message with failed status instead of removing
        console.error('Message send failed:', result.error);
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'failed' }
            : msg
        ));
        
        // Show error banner with retry option
        setErrorBanner({
          message: 'Failed to send message. Tap to retry.',
          onRetry: () => {
            setErrorBanner(null);
            // Retry sending the message
            retryMessage(messageId, newMessage.text, selectedAttachment || undefined);
          }
        });
      }
    } catch (error) {
      console.error('Send message error:', error);
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, status: 'failed' }
          : msg
      ));
      
      // Show error banner with retry option for network errors
      setErrorBanner({
        message: 'Connection error. Tap to retry.',
        onRetry: () => {
          setErrorBanner(null);
          retryMessage(messageId, newMessage.text, selectedAttachment || undefined);
        }
      });
    }
  }, [inputText, isConnected, selectedAttachment, authService, chatService]);

  // Retry message function
  const retryMessage = useCallback(async (messageId: string, messageText: string, attachment?: MediaAttachment) => {
    try {
      const result = await chatService.sendMessage(messageText, attachment);
      
      if (result.success && result.message) {
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'sent', isDelivered: true, id: result.message!.id }
            : msg
        ));
      } else {
        throw new Error(result.error || 'Failed to retry message');
      }
    } catch (error) {
      console.error('Retry message error:', error);
      setErrorBanner({
        message: 'Retry failed. Check your connection.',
        onRetry: () => {
          setErrorBanner(null);
          retryMessage(messageId, messageText, attachment);
        }
      });
    }
  }, [inputText, isConnected, selectedAttachment, authService, chatService]);

  // Removed automated response function
  const handleLogout = useCallback(async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    Alert.alert(
      'Logout',
      'Are you sure you want to logout? This will clear your session.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await authService.logout();
            onLogout();
          },
        },
      ]
    );
  }, [authService, onLogout]);  const initiateVoiceCall = useCallback(async () => {    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    try {
      // Check BLE device connection first
      // TODO: Implement device service check
      const bleConnected = true; // Placeholder - will be implemented with device service
      
      if (!bleConnected) {
        Alert.alert(
          'BLE Required', 
          'Voice calls require BLE authentication. Please ensure your authenticated device is connected.',
          [{ text: 'OK' }]
        );
        return;
      }

      Alert.alert(
        'Secure Voice Call',
        'Start encrypted voice call with voice morphing enabled?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Start Call',
            onPress: async () => {
              try {
                console.log('🎤 Starting voice call with morphing...');
                  // Import VoiceService
                const { VoiceService } = await import('../services/VoiceService');
                const voiceService = VoiceService.getInstance();
                  // Determine user role for morphing profile
                // TODO: Implement getUserRole in AuthService
                const userRole = 'agent'; // Placeholder - will be implemented in AuthService
                
                // Start call with automatic recording and morphing
                const call = await voiceService.startVoiceCall('superuser', userRole);
                
                Alert.alert(
                  'Call Connected', 
                  `Secure voice call active with ${call.morphingProfile} profile. Recording enabled for security.`,
                  [
                    {
                      text: 'End Call',
                      style: 'destructive',
                      onPress: () => voiceService.endVoiceCall()
                    }
                  ]
                );
                
              } catch (error) {
                console.error('❌ Voice call failed:', error);
                Alert.alert('Call Failed', 'Unable to establish secure voice connection.');
              }
            },
          },
        ]
      );    } catch (error) {
      console.error('❌ Voice call initialization failed:', error);
      Alert.alert('Error', 'Unable to initialize voice call system.');
    }
  }, [authService]); // Removed deviceService dependency

  // --- Media and UI Handler Functions ---
  const handleImagePress = useCallback((attachment: MediaAttachment) => {
    if (attachment.uri) {
      setSelectedImage({ visible: true, uri: attachment.uri });
    }
  }, []);

  const handleAttachmentPress = useCallback((attachment: MediaAttachment) => {
    // Open file or show download option
    Alert.alert('Attachment', `File: ${attachment.name}`, [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Download', onPress: () => console.log('Download:', attachment.name) }
    ]);
  }, []);

  const handleRetryUpload = useCallback(async (messageId: string, attachment: MediaAttachment) => {
    if (!attachment.id || !canRetry(attachment.id)) {
      console.log('⚠️ Retry blocked:', {
        hasId: !!attachment.id,
        canRetry: attachment.id ? canRetry(attachment.id) : false,
        cooldown: attachment.id ? retryCooldowns[attachment.id] : undefined
      });
      return;
    }
    
    console.log('🔄 Retrying upload for:', attachment.id, attachment.name);
    setRetryCooldown(attachment.id);
    
    try {
      // Update upload status to uploading
      await mediaCacheManager.updateUploadStatus(attachment.id, 'uploading');
      
      // Find the original message text
      const message = messages.find(msg => msg.id === messageId);
      if (!message) {
        throw new Error('Original message not found');
      }
      
      // Retry the entire send operation
      const result = await chatService.sendMessage(message.text, attachment);
      
      if (result.success) {
        await mediaCacheManager.updateUploadStatus(attachment.id, 'uploaded');
        console.log('✅ Retry successful for:', attachment.id);
      } else {
        throw new Error(result.error || 'Retry failed');
      }
    } catch (error) {
      console.error('❌ Retry upload failed:', error);
      if (attachment.id) {
        await mediaCacheManager.updateUploadStatus(attachment.id, 'failed');
      }
      
      setErrorBanner({
        message: `Retry failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        onRetry: () => {
          setErrorBanner(null);
          setTimeout(() => handleRetryUpload(messageId, attachment), 1000);
        }
      });
    }
  }, [retryCooldowns, messages, chatService, mediaCacheManager]);

  const canRetry = useCallback((attachmentId: string) => {
    const cooldown = retryCooldowns[attachmentId];
    if (!cooldown) return true;
    return Date.now() > cooldown;
  }, [retryCooldowns]);

  const setRetryCooldown = useCallback((attachmentId: string, ms: number = 5000) => {
    setRetryCooldowns(prev => ({ ...prev, [attachmentId]: Date.now() + ms }));
  }, []);

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isUser = item.sender === 'user';
    const hasAttachment = !!item.attachment;

    // Parse reply information from message text
    const replyInfo = parseReplyInfo(item.text || '');
    const replyToMessage = replyInfo.isReply ? findReplyToMessage(replyInfo.replyToId!) : null;
    // --- Robustly infer isImage/isVideo from mimeType if missing ---
    let attachment = item.attachment;
    if (attachment && attachment.mimeType) {
      if (typeof attachment.isImage === 'undefined') {
        attachment.isImage = attachment.mimeType.startsWith('image/');
      }
      if (typeof attachment.isVideo === 'undefined') {
        attachment.isVideo = attachment.mimeType.startsWith('video/');
      }
    }
    const isImageMessage = hasAttachment && attachment?.isImage && attachment?.mimeType?.startsWith('image/');
    const isVideoMessage = hasAttachment && attachment?.isVideo && attachment?.mimeType?.startsWith('video/');
    const isMediaMessage = isImageMessage || isVideoMessage;
    
    // Get cached URI for media display (WhatsApp-like instant preview)
    const getDisplayUri = (attachment: MediaAttachment) => {
      if (attachment.id && (attachment.isImage || attachment.isVideo)) {
        if (attachment.isVideo && attachment.thumbnailUri) {
          return attachment.thumbnailUri;
        }
        const cachedUri = mediaCacheManager.getDisplayUriSync(attachment.id);
        if (cachedUri && typeof cachedUri === 'string' && cachedUri.trim() !== '') {
          return cachedUri;
        }
      }
      if (attachment.uri && typeof attachment.uri === 'string' && attachment.uri.trim() !== '') {
        return attachment.uri;
      }
      return null; // Never return ''
    };
    
    // Get appropriate media label for status text (without showing random IDs)
    const getMediaLabel = (attachment: MediaAttachment) => {
      if (attachment.isImage) return 'Photo';
      if (attachment.isVideo) return 'Video';
      if (attachment.isAudio) return 'Audio';
      // For documents, use a simplified name without random UUIDs
      const simplifiedName = attachment.name
        .replace(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i, '')  // Remove UUIDs
        .replace(/\d+_\w+/g, '')  // Remove patterns like 12345_a1b2c3
        .replace(/^attachment_\d+_\w+/g, '')  // Remove attachment_123456_a1b2c3 patterns
        .replace(/\s+/g, ' ')  // Replace multiple spaces with a single space
        .trim();
      return simplifiedName || 'File';
    };
    
    return (
      <SwipeableMessage
        onSwipeToReply={() => setReplyToMessage(item)}
        isUser={isUser}
      >
        <View style={[
          chatStyles.messageContainer,
          isUser ? chatStyles.userMessageContainer : chatStyles.receivedMessageContainer
        ]}>
          {/* For media messages (images/videos), render without the bubble padding */}
          {isMediaMessage ? (
            <Animated.View style={[
              chatStyles.imageMessageBubble,
              isUser ? chatStyles.userImageMessage : chatStyles.receivedImageMessage,
              { shadowColor: isUser ? '#007AFF' : '#000', shadowOpacity: 0.15, shadowRadius: 6, elevation: 4, marginBottom: 6, }
            ]}>
              <TouchableOpacity 
                style={chatStyles.imageAttachment}
                onPress={() => handleImagePress(attachment!)}
                activeOpacity={0.85}
                onLongPress={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy)}
                accessibilityLabel={isVideoMessage ? 'View video' : 'View image'}
              >
                {isVideoMessage ? (
                  <View style={chatStyles.videoContainer}>
                    {(() => {
                      const thumbUri = attachment!.thumbnailUri;
                      const displayUri = getDisplayUri(attachment!);
                      
                      // Logic: For videos, prioritize thumbnails and never try to load video files directly in Image components
                      if (thumbUri && thumbUri.trim() !== '') {
                        return (
                          <FadeInMedia
                            uri={thumbUri}
                            style={chatStyles.attachmentImage}
                            resizeMode="cover"
                            onError={(error) => {
                              console.warn('🎥 Video thumbnail load error:', error.nativeEvent.error, 'for:', attachment!.name);
                            }}
                            fallbackIcon="🎥"
                            fallbackText="Video preview"
                            fallbackStyle={chatStyles.mediaErrorPlaceholder}
                          />
                        );
                      } else if (displayUri && !displayUri.endsWith('.mov') && !displayUri.endsWith('.mp4')) {
                        // Only use displayUri if it's not a video file (likely a thumbnail)
                        return (
                          <FadeInMedia
                            uri={displayUri}
                            style={chatStyles.attachmentImage}
                            resizeMode="cover"
                            onError={(error) => {
                              console.warn('🎥 Video preview load error:', error.nativeEvent.error, 'for:', attachment!.name);
                            }}
                            fallbackIcon="🎥"
                            fallbackText="Video preview"
                            fallbackStyle={chatStyles.mediaErrorPlaceholder}
                          />
                        );
                      } else {
                        // Fallback to placeholder for videos with no thumbnails
                        return (
                          <View style={[chatStyles.attachmentImage, chatStyles.mediaErrorPlaceholder]}>
                            <Text style={chatStyles.mediaErrorIcon}>🎥</Text>
                            <Text style={chatStyles.mediaErrorText}>Video</Text>
                            <Text style={[chatStyles.mediaErrorText, {fontSize: 12, marginTop: 2}]}>
                              {Math.round(attachment!.size / (1024 * 1024) * 10) / 10} MB
                            </Text>
                          </View>
                        );
                      }
                    })()}
                    <Animated.View style={[chatStyles.videoPlayButton, { opacity: 0.92 }]}> 
                      <Text style={chatStyles.playIcon}>▶️</Text>
                    </Animated.View>
                  </View>
                ) : (
                  (() => {
                    const displayUri = getDisplayUri(attachment!);
                    if (displayUri) {
                      return (
                        <FadeInMedia
                          uri={displayUri && displayUri.trim() !== '' ? displayUri : null}
                          style={chatStyles.attachmentImage}
                          resizeMode="cover"
                          onError={(error) => {
                            console.warn('📸 Image load error:', error.nativeEvent.error, 'for:', attachment!.name);
                          }}
                          fallbackIcon="🖼️"
                          fallbackText="Image unavailable"
                          fallbackStyle={chatStyles.mediaErrorPlaceholder}
                        />
                      );
                    } else {
                      return (
                        <View style={[chatStyles.attachmentImage, chatStyles.mediaErrorPlaceholder]}>
                          <Text style={chatStyles.mediaErrorIcon}>🖼️</Text>
                          <Text style={chatStyles.mediaErrorText}>Image unavailable</Text>
                        </View>
                      );
                    }
                  })()
                )}
              </TouchableOpacity>
              
              {/* Upload Progress Overlay */}
              {attachment?.id && (
                <UploadProgressOverlay
                  uploadStatus={mediaCacheManager.getCachedMediaInfo(attachment.id)?.uploadStatus || 'pending'}
                  uploadProgress={mediaCacheManager.getCachedMediaInfo(attachment.id)?.uploadProgress || 0}
                  onRetry={() => handleRetryUpload(item.id, attachment)}
                  retryDisabled={!canRetry(attachment.id)}
                />
              )}
              
              {/* Media Message Footer (timestamp inside image) */}
              <View style={chatStyles.imageMessageFooter}>
                <Text style={chatStyles.imageTimestamp}>
                  {isVideoMessage ? '🎥 Video' : '📷 Photo'} • {new Date(item.timestamp).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </Text>
                {/* Show failed status for user messages */}
                {isUser && item.status === 'failed' && (
                  <Text style={chatStyles.failedStatus}>⚠️</Text>
                )}
              </View>
            </Animated.View>
          ) : (
            /* Regular text messages */
            <Animated.View style={[
              chatStyles.messageBubble,
              isUser ? chatStyles.userMessage : chatStyles.receivedMessage,
              { shadowColor: isUser ? '#007AFF' : '#000', shadowOpacity: 0.08, shadowRadius: 4, elevation: 2, }
            ]}>
              {/* Reply Preview */}
              {replyToMessage && (
                <View style={chatStyles.replyMessagePreview}>
                  <Text style={chatStyles.replyToText}>
                    {replyToMessage.sender === 'user' ? 'You' : 'Support'}
                  </Text>
                  <Text style={chatStyles.replyMessageText} numberOfLines={2}>
                    {replyToMessage.text || '📎 Attachment'}
                  </Text>
                </View>
              )}
              
              {/* Attachment (Non-media files) */}
              {hasAttachment && !isMediaMessage && (
                <TouchableOpacity 
                  style={chatStyles.fileAttachment}
                  onPress={() => handleAttachmentPress(attachment!)}
                  activeOpacity={0.8}
                >
                  <Text style={chatStyles.fileIcon}>
                    {attachment!.mimeType?.startsWith('audio/') ? '🎵' : '📄'}
                  </Text>
                  <View style={chatStyles.fileInfo}>
                    <Text style={chatStyles.fileName} numberOfLines={1}>
                      {attachment!.name}
                    </Text>
                    <Text style={chatStyles.fileSize}>
                      {(attachment!.size / 1024).toFixed(1)}KB
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              
              {/* Message Text */}
              {replyInfo.actualText && !replyInfo.actualText.startsWith('📎') && replyInfo.actualText.trim() !== '' && (
                <Text style={[
                  chatStyles.messageText,
                  isUser ? chatStyles.userMessageText : chatStyles.receivedMessageText
                ]}>
                  {replyInfo.actualText}
                </Text>
              )}
              
              {/* Message Footer */}
              <View style={chatStyles.messageFooter}>
                <Text style={[
                  chatStyles.timestamp,
                  isUser ? chatStyles.userTimestamp : chatStyles.receivedTimestamp
                ]}>
                  {new Date(item.timestamp).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </Text>
                {/* Show failed status for user messages */}
                {isUser && item.status === 'failed' && (
                  <Text style={chatStyles.failedStatus}>⚠️</Text>
                )}
              </View>
            </Animated.View>
          )}
        </View>
      </SwipeableMessage>
    );
  };

  return (
    <GestureHandlerRootView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Header */}
      <SafeAreaView style={styles.headerSafeArea}>
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
              <Text style={styles.logoutText}>← Exit</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>Administrator</Text>
            {/* Removed status indicator for clean header */}
          </View>
          
          <View style={styles.headerRight}>
            <TouchableOpacity onPress={initiateVoiceCall} style={styles.callButton}>
              <Text style={styles.callButtonText}>📞</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>

      {/* Error Banner */}
      {errorBanner && (
        <ErrorBanner
          message={errorBanner.message}
          onRetry={errorBanner.onRetry}
          onDismiss={() => setErrorBanner(null)}
        />
      )}

      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Messages List */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={[styles.messagesContent, { paddingBottom: 20 }]}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => {
            setTimeout(() => {
              flatListRef.current?.scrollToEnd({ animated: true });
            }, 100);
          }}
        />

        {/* Note: No typing indicators as per requirements */}

        {/* Input Area */}
        <SafeAreaView style={styles.inputSafeArea}>
          {/* Reply Bar - WhatsApp-like reply preview */}
          {replyToMessage && (
            <View style={chatStyles.replyBar}>
              <View style={chatStyles.replyPreview}>
                <View style={chatStyles.replyIndicator} />
                <View style={chatStyles.replyContent}>
                  <Text style={chatStyles.replyAuthor}>
                    {replyToMessage.sender === 'user' ? 'You' : 'Support'}
                  </Text>
                  <Text style={chatStyles.replyText} numberOfLines={1}>
                    {replyToMessage.attachment ? '📎 Attachment' : replyToMessage.text}
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                style={chatStyles.replyCloseButton}
                onPress={() => setReplyToMessage(null)}
                accessibilityLabel="Cancel reply"
              >
                <Text style={chatStyles.replyCloseText}>×</Text>
              </TouchableOpacity>
            </View>
          )}
          
          {/* Attachment Preview */}
          {selectedAttachment && (
            <View style={styles.attachmentPreview}>
              {(selectedAttachment.isImage || selectedAttachment.isVideo) ? (
                <View style={styles.mediaPreviewContainer}>
                  <Image 
                    source={{ uri: selectedAttachment.uri }}
                    style={styles.mediaPreviewImage}
                    resizeMode="cover"
                  />
                  {selectedAttachment.isVideo && (
                    <View style={styles.videoPlayOverlay}>
                      <Text style={{ fontSize: 40 }}>▶️</Text>
                    </View>
                  )}
                  <View style={styles.mediaPreviewDetails}>
                    <Text style={styles.mediaPreviewSize}>
                      {(selectedAttachment.size / (1024 * 1024)).toFixed(1)} MB
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.removeAttachmentButton}
                    onPress={removeAttachment}
                  >
                    <Text style={styles.removeAttachmentText}>×</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.attachmentPreviewInfo}>
                  <Text style={styles.attachmentPreviewIcon}>
                    {selectedAttachment.isAudio ? '🎵' : '📄'}
                  </Text>
                  <View style={styles.attachmentPreviewDetails}>
                    <Text style={styles.attachmentPreviewName} numberOfLines={1}>
                      {selectedAttachment.name}
                    </Text>
                    <Text style={styles.attachmentPreviewSize}>
                      {selectedAttachment.size > 1024 * 1024 ? 
                        `${(selectedAttachment.size / (1024 * 1024)).toFixed(1)} MB` : 
                        `${Math.round(selectedAttachment.size / 1024)} KB`}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.removeAttachmentButton}
                    onPress={removeAttachment}
                  >
                    <Text style={styles.removeAttachmentText}>×</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          )}
          
          <View style={styles.inputContainer}>
            <TouchableOpacity
              style={styles.attachmentButton}
              onPress={handleAttachment}
            >
              <Text style={styles.attachmentButtonText}>+</Text>
            </TouchableOpacity>
            
            <View style={styles.textInputContainer}>
              <TextInput
                style={[
                  styles.textInput,
                  { height: Math.max(40, Math.min(100, inputText.split('\n').length * 20 + 20)) }
                ]}
                value={inputText}
                onChangeText={setInputText}
                placeholder="Message"
                placeholderTextColor="#8E8E93"
                multiline
                maxLength={1000}
                editable={isConnected}
                onFocus={() => {
                  setTimeout(() => {
                    flatListRef.current?.scrollToEnd({ animated: true });
                  }, 300);
                }}
              />
            </View>
            <TouchableOpacity
              style={[
                styles.sendButton,
                ((!inputText.trim() && !selectedAttachment) || !isConnected) && styles.sendButtonDisabled
              ]}
              onPress={sendMessage}
              disabled={(!inputText.trim() && !selectedAttachment) || !isConnected}
            >
              <Text style={styles.sendButtonText}>→</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </KeyboardAvoidingView>

      {/* Attachment Picker */}
      <AttachmentPicker
        visible={showAttachmentPicker}
        onClose={() => setShowAttachmentPicker(false)}
        onSelect={handleAttachmentSelect}
      />

      {/* Image Viewer Modal */}
      <ImageViewer
        visible={selectedImage.visible}
        imageUri={selectedImage.uri}
        onClose={() => setSelectedImage({ visible: false, uri: '' })}
      />
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7', // iOS system background
  },
  headerSafeArea: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 0.5,
    borderBottomColor: '#D1D1D6',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  chatContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    height: 56,
    justifyContent: 'space-between',
  },
  headerLeft: {
    flex: 1,
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000000',
    fontFamily: '-apple-system',
  },
  headerSubtitle: {
    fontSize: 12,
    color: 'rgba(0, 0, 0, 0.5)',
    fontFamily: '-apple-system',
    marginTop: 1,
  },
  logoutButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  logoutText: {
    fontSize: 16,
    color: '#007AFF',
    fontFamily: '-apple-system',
    fontWeight: '500',
  },
  callButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  callButtonText: {
    fontSize: 16,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: theme.spacing.md,
  },
  messageContainer: {
    paddingHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.xs / 2,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  superuserMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userMessage: {
    backgroundColor: theme.colors.systemBlue,
    borderBottomRightRadius: 6,
  },
  superuserMessage: {
    backgroundColor: theme.colors.systemGray5,
    borderBottomLeftRadius: 6,
  },
  messageText: {
    fontSize: theme.typography.sizes.body,
    lineHeight: 22,
    fontFamily: theme.typography.families.systemText,
  },
  userMessageText: {
    color: theme.colors.background,
  },
  superuserMessageText: {
    color: theme.colors.label,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: theme.spacing.xs,
  },
  timestamp: {
    fontSize: theme.typography.sizes.caption2,
    fontFamily: theme.typography.families.system,
  },
  userTimestamp: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  superuserTimestamp: {
    color: theme.colors.tertiaryLabel,
  },
  typingContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
  },
  typingText: {
    fontSize: theme.typography.sizes.caption,
    color: theme.colors.secondaryLabel,
    fontStyle: 'italic',
    fontFamily: theme.typography.families.system,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 0.5,
    borderTopColor: '#D1D1D6',
  },
  inputSafeArea: {
    backgroundColor: '#FFFFFF',
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    borderRadius: 22,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 8,
    minHeight: 44,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  textInput: {
    fontSize: 16,
    color: '#000000',
    fontFamily: Platform.OS === 'ios' ? '-apple-system' : 'Roboto',
    textAlignVertical: 'center',
    paddingTop: 0,
    paddingBottom: 0,
    lineHeight: 20,
    maxHeight: 100,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  sendButtonDisabled: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    shadowOpacity: 0,
    elevation: 0,
  },
  sendButtonText: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  // Attachment Styles
  attachmentContainer: {
    marginBottom: 8,
  },
  imageAttachment: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  attachmentImage: {
    width: 200,
    height: 150,
    borderRadius: 12,
  },
  fileAttachment: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 8,
  },
  attachmentIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  attachmentInfo: {
    flex: 1,
  },
  attachmentName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  attachmentSize: {
    fontSize: 12,
    opacity: 0.7,
  },
  userAttachmentText: {
    color: 'white',
  },
  superuserAttachmentText: {
    color: theme.colors.label,
  },
  // Attachment Preview Styles
  attachmentPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.systemGray6,
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 8,
    overflow: 'hidden',
  },
  mediaPreviewContainer: {
    width: '100%',
    height: 160,
    position: 'relative',
  },
  mediaPreviewImage: {
    width: '100%',
    height: '100%',
  },
  videoPlayOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  mediaPreviewDetails: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    backgroundColor: 'rgba(0,0,0,0.4)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
  },
  mediaPreviewSize: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  attachmentPreviewInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  attachmentPreviewIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  attachmentPreviewDetails: {
    flex: 1,
  },
  attachmentPreviewName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.label,
    marginBottom: 2,
  },
  attachmentPreviewSize: {
    fontSize: 12,
    color: theme.colors.secondaryLabel,
  },
  removeAttachmentButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.systemRed,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  removeAttachmentText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  attachmentButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
  attachmentButtonText: {
    fontSize: 20,
    color: '#007AFF',
    fontWeight: '500',
  },
});

export default ChatScreen;
