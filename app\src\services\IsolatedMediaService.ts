/**
 * Isolated Media Service
 * Implements Instagram-like image selection that only shows selected images
 * Prevents access to full photo library for privacy
 */

import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import { MediaAttachment } from './MediaService';

interface IsolatedMediaItem {
  id: string;
  uri: string;
  name: string;
  type: string;
  size: number;
  mimeType: string;
  isSelected: boolean;
  isolatedUri?: string; // Isolated copy in app directory
  thumbnailUri?: string;
}

interface MediaSelectionOptions {
  allowsEditing?: boolean;
  quality?: number;
  allowsMultipleSelection?: boolean;
  mediaTypes?: 'images' | 'videos' | 'all';
}

export class IsolatedMediaService {
  private isolatedMediaDir: string;
  private selectedMedia: Map<string, IsolatedMediaItem> = new Map();

  constructor() {
    this.isolatedMediaDir = `${FileSystem.documentDirectory}CCALC_Isolated_Media/`;
    this.initializeIsolatedDirectory();
  }

  /**
   * Initialize isolated media directory
   */
  private async initializeIsolatedDirectory(): Promise<void> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.isolatedMediaDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.isolatedMediaDir, { intermediates: true });
        console.log('✅ Isolated media directory created');
      }
    } catch (error) {
      console.error('❌ Failed to create isolated media directory:', error);
    }
  }

  /**
   * Request camera permissions
   */
  private async requestCameraPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please grant camera permission to take photos.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('❌ Camera permission error:', error);
      return false;
    }
  }

  /**
   * Request media library permissions (limited access)
   */
  private async requestMediaLibraryPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Photo Library Permission Required',
          'Please grant limited photo access to select images.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('❌ Media library permission error:', error);
      return false;
    }
  }

  /**
   * Take photo with camera (Instagram-like)
   */
  public async takePhoto(options: MediaSelectionOptions = {}): Promise<MediaAttachment | null> {
    try {
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) return null;

      console.log('📷 Opening camera for photo capture...');

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing ?? true,
        quality: options.quality ?? 0.8,
        base64: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📷 Camera capture cancelled');
        return null;
      }

      const asset = result.assets[0];
      
      // Create isolated copy
      const isolatedMedia = await this.createIsolatedCopy(asset);
      if (!isolatedMedia) return null;

      // Add to selected media
      this.selectedMedia.set(isolatedMedia.id, isolatedMedia);

      console.log('✅ Photo captured and isolated:', isolatedMedia.name);

      return this.convertToMediaAttachment(isolatedMedia);

    } catch (error) {
      console.error('❌ Camera capture error:', error);
      Alert.alert('Error', 'Failed to capture photo. Please try again.');
      return null;
    }
  }

  /**
   * Select image from library (limited access)
   */
  public async selectFromLibrary(options: MediaSelectionOptions = {}): Promise<MediaAttachment | null> {
    try {
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) return null;

      console.log('📱 Opening photo library with limited access...');

      const mediaTypeOptions = options.mediaTypes === 'videos' 
        ? ImagePicker.MediaTypeOptions.Videos
        : options.mediaTypes === 'all'
        ? ImagePicker.MediaTypeOptions.All
        : ImagePicker.MediaTypeOptions.Images;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: mediaTypeOptions,
        allowsEditing: options.allowsEditing ?? true,
        quality: options.quality ?? 0.8,
        allowsMultipleSelection: options.allowsMultipleSelection ?? false,
        base64: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📱 Library selection cancelled');
        return null;
      }

      const asset = result.assets[0]; // For now, handle single selection
      
      // Create isolated copy
      const isolatedMedia = await this.createIsolatedCopy(asset);
      if (!isolatedMedia) return null;

      // Add to selected media
      this.selectedMedia.set(isolatedMedia.id, isolatedMedia);

      console.log('✅ Image selected and isolated:', isolatedMedia.name);

      return this.convertToMediaAttachment(isolatedMedia);

    } catch (error) {
      console.error('❌ Library selection error:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
      return null;
    }
  }

  /**
   * Select document (for non-media files)
   */
  public async selectDocument(): Promise<MediaAttachment | null> {
    try {
      console.log('📄 Opening document picker...');

      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📄 Document selection cancelled');
        return null;
      }

      const asset = result.assets[0];
      
      // Create isolated copy
      const isolatedMedia = await this.createIsolatedCopy({
        uri: asset.uri,
        fileName: asset.name,
        fileSize: asset.size,
        mimeType: asset.mimeType || 'application/octet-stream',
      });

      if (!isolatedMedia) return null;

      // Add to selected media
      this.selectedMedia.set(isolatedMedia.id, isolatedMedia);

      console.log('✅ Document selected and isolated:', isolatedMedia.name);

      return this.convertToMediaAttachment(isolatedMedia);

    } catch (error) {
      console.error('❌ Document selection error:', error);
      Alert.alert('Error', 'Failed to select document. Please try again.');
      return null;
    }
  }

  /**
   * Create isolated copy of selected media
   */
  private async createIsolatedCopy(asset: any): Promise<IsolatedMediaItem | null> {
    try {
      const id = `isolated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const originalUri = asset.uri;
      const fileName = asset.fileName || asset.name || `media_${id}`;
      const fileSize = asset.fileSize || asset.size || 0;
      const mimeType = asset.mimeType || asset.type || this.getMimeTypeFromUri(originalUri);

      // Create isolated file path
      const fileExtension = this.getFileExtension(fileName);
      const isolatedFileName = `${id}${fileExtension}`;
      const isolatedUri = `${this.isolatedMediaDir}${isolatedFileName}`;

      // Copy file to isolated directory
      await FileSystem.copyAsync({
        from: originalUri,
        to: isolatedUri,
      });

      // Verify the copy
      const fileInfo = await FileSystem.getInfoAsync(isolatedUri);
      if (!fileInfo.exists) {
        throw new Error('Failed to create isolated copy');
      }

      // Generate thumbnail for images/videos
      let thumbnailUri: string | undefined;
      if (mimeType.startsWith('image/')) {
        thumbnailUri = isolatedUri; // For images, use the image itself as thumbnail
      }

      const isolatedMedia: IsolatedMediaItem = {
        id,
        uri: originalUri,
        name: fileName,
        type: this.getFileCategory(mimeType),
        size: fileSize,
        mimeType,
        isSelected: true,
        isolatedUri,
        thumbnailUri,
      };

      console.log('✅ Created isolated copy:', {
        id,
        name: fileName,
        size: this.formatFileSize(fileSize),
        isolatedPath: isolatedFileName
      });

      return isolatedMedia;

    } catch (error) {
      console.error('❌ Failed to create isolated copy:', error);
      return null;
    }
  }

  /**
   * Convert isolated media to MediaAttachment format
   */
  private convertToMediaAttachment(isolatedMedia: IsolatedMediaItem): MediaAttachment {
    return {
      id: isolatedMedia.id,
      name: isolatedMedia.name,
      type: isolatedMedia.type,
      size: isolatedMedia.size,
      uri: isolatedMedia.isolatedUri || isolatedMedia.uri, // Use isolated URI
      mimeType: isolatedMedia.mimeType,
      isImage: isolatedMedia.mimeType.startsWith('image/'),
      isVideo: isolatedMedia.mimeType.startsWith('video/'),
      isAudio: isolatedMedia.mimeType.startsWith('audio/'),
      thumbnailUri: isolatedMedia.thumbnailUri,
    };
  }

  /**
   * Get all selected (isolated) media
   */
  public getSelectedMedia(): MediaAttachment[] {
    return Array.from(this.selectedMedia.values()).map(item => 
      this.convertToMediaAttachment(item)
    );
  }

  /**
   * Remove media from selection and delete isolated copy
   */
  public async removeSelectedMedia(mediaId: string): Promise<boolean> {
    try {
      const media = this.selectedMedia.get(mediaId);
      if (!media) return false;

      // Delete isolated file
      if (media.isolatedUri) {
        const fileInfo = await FileSystem.getInfoAsync(media.isolatedUri);
        if (fileInfo.exists) {
          await FileSystem.deleteAsync(media.isolatedUri);
          console.log('🗑️ Deleted isolated media file:', media.name);
        }
      }

      // Remove from selection
      this.selectedMedia.delete(mediaId);
      console.log('✅ Removed media from selection:', media.name);

      return true;

    } catch (error) {
      console.error('❌ Failed to remove selected media:', error);
      return false;
    }
  }

  /**
   * Clear all selected media
   */
  public async clearAllSelectedMedia(): Promise<void> {
    try {
      const mediaIds = Array.from(this.selectedMedia.keys());
      
      for (const mediaId of mediaIds) {
        await this.removeSelectedMedia(mediaId);
      }

      console.log('✅ Cleared all selected media');

    } catch (error) {
      console.error('❌ Failed to clear selected media:', error);
    }
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot > 0 ? fileName.substring(lastDot) : '';
  }

  /**
   * Get MIME type from URI
   */
  private getMimeTypeFromUri(uri: string): string {
    const extension = this.getFileExtension(uri).toLowerCase();
    
    const mimeTypes: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.mp4': 'video/mp4',
      '.mov': 'video/quicktime',
      '.avi': 'video/x-msvideo',
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * Get file category from MIME type
   */
  private getFileCategory(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf') || mimeType.includes('document')) return 'document';
    return 'file';
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get storage usage statistics
   */
  public async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    formattedSize: string;
  }> {
    try {
      let totalSize = 0;
      let totalFiles = 0;

      for (const media of this.selectedMedia.values()) {
        if (media.isolatedUri) {
          const fileInfo = await FileSystem.getInfoAsync(media.isolatedUri);
          if (fileInfo.exists && fileInfo.size) {
            totalSize += fileInfo.size;
            totalFiles++;
          }
        }
      }

      return {
        totalFiles,
        totalSize,
        formattedSize: this.formatFileSize(totalSize),
      };

    } catch (error) {
      console.error('❌ Failed to get storage stats:', error);
      return {
        totalFiles: 0,
        totalSize: 0,
        formattedSize: '0 Bytes',
      };
    }
  }
}

export default IsolatedMediaService;
