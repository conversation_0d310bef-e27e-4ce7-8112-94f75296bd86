import sharp from 'sharp';
import ffmpeg from 'fluent-ffmpeg';
import path from 'path';

export async function generateThumbnail(input: string | Buffer, mimeType: string): Promise<Buffer | null> {
  try {
    if (mimeType.startsWith('image/')) {
      // Handle both file path and buffer input
      let sharpInput: string | Buffer;
      if (typeof input === 'string') {
        // If it's a base64 string, convert to buffer
        if (input.startsWith('data:') || input.match(/^[A-Za-z0-9+/=]+$/)) {
          sharpInput = Buffer.from(input.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64');
        } else {
          // It's a file path
          sharpInput = input;
        }
      } else {
        // It's already a buffer
        sharpInput = input;
      }

      return await sharp(sharpInput)
        .resize(200, 200, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({ quality: 80 })
        .toBuffer();
    }
    
    if (mimeType.startsWith('video/')) {
      // For video thumbnails, we need a file path, not a buffer
      if (typeof input !== 'string' || input.match(/^[A-Za-z0-9+/=]+$/)) {
        console.warn('Video thumbnail generation requires file path, not buffer/base64');
        return null;
      }

      return new Promise((resolve, reject) => {
        const outputPath = path.join(process.cwd(), 'temp', `thumb_${Date.now()}.jpg`);

        ffmpeg(input)
          .screenshot({
            timestamps: ['50%'],
            filename: path.basename(outputPath),
            folder: path.dirname(outputPath),
            size: '200x200'
          })
          .on('end', async () => {
            try {
              const thumbnail = await sharp(outputPath)
                .jpeg({ quality: 80 })
                .toBuffer();
              resolve(thumbnail);
            } catch (error) {
              reject(error);
            }
          })
          .on('error', reject);
      });
    }
    
    return null;
  } catch (error) {
    console.error('Thumbnail generation error:', error);
    return null;
  }
}
